#!/bin/bash

# =============================================================================
# 自动化部署脚本 - sanyth-xuegong-5-server
# 功能：Maven构建、文件传输、服务重启、部署验证
# 作者：自动生成
# 日期：$(date +%Y-%m-%d)
# =============================================================================

# 脚本配置
set -e  # 遇到错误立即退出
set -o pipefail  # 管道命令中任何一个失败都会导致整个管道失败

# =============================================================================
# 配置区域 - 请根据实际环境修改以下配置
# =============================================================================

# 服务器连接配置
SERVER_HOST="${DEPLOY_HOST:-*************}"  # 目标服务器IP
SERVER_USER="${DEPLOY_USER:-root}"            # SSH用户名
SERVER_PORT="${DEPLOY_PORT:-22}"             # SSH端口
SERVER_PASSWORD="${DEPLOY_PASSWORD:-}"       # SSH密码（如果未设置将提示输入）

# 路径配置
LOCAL_JAR_PATH="xggl-api/target/xggl-1.0-SNAPSHOT-api.jar"
REMOTE_APP_DIR="/home/<USER>/xuegong-v5"
REMOTE_JAR_PATH="${REMOTE_APP_DIR}/xggl-1.0-SNAPSHOT-api.jar"
REMOTE_BACKUP_DIR="${REMOTE_APP_DIR}/backup"

# 超时配置
SHUTDOWN_TIMEOUT=30  # 服务停止超时时间（秒）
STARTUP_TIMEOUT=60   # 服务启动超时时间（秒）
HEALTH_CHECK_TIMEOUT=120  # 健康检查超时时间（秒）

# 日志配置
LOG_FILE="deploy_$(date +%Y%m%d_%H%M%S).log"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# =============================================================================
# 工具函数
# =============================================================================

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
}

log_warn() {
    log "WARN" "$@"
}

log_error() {
    log "ERROR" "$@"
}

log_success() {
    log "SUCCESS" "$@"
}

# 错误处理函数
handle_error() {
    local exit_code=$?
    local line_number=$1
    log_error "脚本在第 $line_number 行执行失败，退出码: $exit_code"
    log_error "部署失败，请检查日志文件: $LOG_FILE"
    exit $exit_code
}

# 设置错误陷阱
trap 'handle_error $LINENO' ERR

# 进度显示函数
show_progress() {
    local current=$1
    local total=$2
    local desc=$3
    local percent=$((current * 100 / total))
    printf "\r[%3d%%] %s" $percent "$desc"
    if [ $current -eq $total ]; then
        echo ""
    fi
}

# 获取SSH密码（如果未设置）
get_ssh_password() {
    if [ -z "$SERVER_PASSWORD" ]; then
        echo -n "请输入SSH密码 ($SERVER_USER@$SERVER_HOST): "
        read -s SERVER_PASSWORD
        echo ""

        if [ -z "$SERVER_PASSWORD" ]; then
            log_error "密码不能为空"
            exit 1
        fi
    fi
}

# SSH连接测试
test_ssh_connection() {
    log_info "测试SSH连接到 $SERVER_USER@$SERVER_HOST:$SERVER_PORT"

    # 确保有密码
    get_ssh_password

    if sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" -o ConnectTimeout=10 -o BatchMode=yes \
           -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "echo 'SSH连接测试成功'" >/dev/null 2>&1; then
        log_success "SSH连接测试成功"
        return 0
    else
        log_error "SSH连接失败，请检查网络连接、用户名和密码"
        return 1
    fi
}

# 远程命令执行函数
remote_exec() {
    local command="$1"
    local timeout="${2:-30}"

    sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" -o ConnectTimeout=10 \
        -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "timeout $timeout bash -c '$command'"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具和环境"
    
    local missing_tools=()
    
    # 检查Maven
    if ! command -v mvn >/dev/null 2>&1; then
        missing_tools+=("maven")
    fi
    
    # 检查SSH
    if ! command -v ssh >/dev/null 2>&1; then
        missing_tools+=("ssh")
    fi
    
    # 检查SCP
    if ! command -v scp >/dev/null 2>&1; then
        missing_tools+=("scp")
    fi

    # 检查sshpass
    if ! command -v sshpass >/dev/null 2>&1; then
        missing_tools+=("sshpass")
    fi

    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要的工具: ${missing_tools[*]}"
        log_error "请安装缺少的工具后重新运行脚本"
        log_error "安装命令示例："
        log_error "  Ubuntu/Debian: sudo apt-get install sshpass"
        log_error "  CentOS/RHEL: sudo yum install sshpass"
        log_error "  macOS: brew install sshpass"
        exit 1
    fi
    
    # 检查项目根目录
    if [ ! -f "pom.xml" ]; then
        log_error "未找到pom.xml文件，请确保在项目根目录下运行此脚本"
        exit 1
    fi
    
    log_success "环境检查完成"
}

# =============================================================================
# 主要功能函数
# =============================================================================

# 步骤1: Maven构建
maven_build() {
    log_info "开始Maven构建..."
    show_progress 1 5 "正在执行Maven构建"
    
    # 清理之前的构建产物
    if [ -f "$LOCAL_JAR_PATH" ]; then
        rm -f "$LOCAL_JAR_PATH"
        log_info "清理旧的JAR文件"
    fi
    
    # 执行Maven构建
    log_info "执行命令: mvn clean package -DskipTests"
    if mvn clean package -DskipTests >> "$LOG_FILE" 2>&1; then
        log_success "Maven构建成功"
    else
        log_error "Maven构建失败，请检查构建日志"
        exit 1
    fi
    
    # 验证JAR文件是否生成
    if [ ! -f "$LOCAL_JAR_PATH" ]; then
        log_error "构建完成但未找到JAR文件: $LOCAL_JAR_PATH"
        exit 1
    fi
    
    local jar_size=$(du -h "$LOCAL_JAR_PATH" | cut -f1)
    log_success "JAR文件构建成功，大小: $jar_size"
}

# 步骤2: 文件传输
transfer_files() {
    log_info "开始文件传输..."
    show_progress 2 5 "正在传输JAR文件到目标服务器"
    
    # 测试SSH连接
    test_ssh_connection
    
    # 创建远程备份目录
    log_info "创建远程备份目录"
    remote_exec "mkdir -p $REMOTE_BACKUP_DIR" 10
    
    # 备份现有JAR文件
    if remote_exec "[ -f '$REMOTE_JAR_PATH' ]" 5; then
        local backup_name="xggl-1.0-SNAPSHOT-api.jar.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "备份现有JAR文件为: $backup_name"
        remote_exec "cp '$REMOTE_JAR_PATH' '$REMOTE_BACKUP_DIR/$backup_name'" 10
    fi
    
    # 传输新的JAR文件
    log_info "传输JAR文件到目标服务器"
    if sshpass -p "$SERVER_PASSWORD" scp -P "$SERVER_PORT" -o ConnectTimeout=30 \
           -o StrictHostKeyChecking=no "$LOCAL_JAR_PATH" "$SERVER_USER@$SERVER_HOST:$REMOTE_JAR_PATH" >> "$LOG_FILE" 2>&1; then
        log_success "文件传输成功"
    else
        log_error "文件传输失败"
        exit 1
    fi
    
    # 验证传输的文件
    local remote_size=$(remote_exec "du -h '$REMOTE_JAR_PATH' | cut -f1" 5)
    log_success "远程JAR文件大小: $remote_size"
}

# 步骤3: 停止服务
stop_service() {
    log_info "开始停止服务..."
    show_progress 3 5 "正在停止应用服务"
    
    # 首先尝试使用shutdown脚本
    log_info "尝试使用shutdown.sh脚本停止服务"
    if remote_exec "cd '$REMOTE_APP_DIR' && [ -f './bin/shutdown.sh' ] && ./bin/shutdown.sh" $SHUTDOWN_TIMEOUT; then
        log_success "使用shutdown.sh成功停止服务"
        sleep 3  # 等待服务完全停止
        return 0
    else
        log_warn "shutdown.sh脚本执行失败或不存在，尝试强制停止"
    fi
    
    # 查找并强制终止Java进程
    log_info "查找相关Java进程"
    local java_pids=$(remote_exec "ps aux | grep 'xggl.*jar' | grep -v grep | awk '{print \$2}'" 10 || echo "")
    
    if [ -n "$java_pids" ]; then
        log_info "找到Java进程: $java_pids"
        
        # 首先尝试优雅停止
        log_info "尝试优雅停止Java进程"
        remote_exec "echo '$java_pids' | xargs -r kill -15" 10 || true
        
        # 等待进程停止
        sleep 5
        
        # 检查进程是否仍在运行
        local remaining_pids=$(remote_exec "ps aux | grep 'xggl.*jar' | grep -v grep | awk '{print \$2}'" 5 || echo "")
        
        if [ -n "$remaining_pids" ]; then
            log_warn "进程仍在运行，执行强制终止"
            remote_exec "echo '$remaining_pids' | xargs -r kill -9" 10 || true
            sleep 2
        fi
        
        log_success "Java进程已停止"
    else
        log_info "未找到运行中的Java进程"
    fi
}

# 步骤4: 启动服务
start_service() {
    log_info "开始启动服务..."
    show_progress 4 5 "正在启动应用服务"

    # 检查startup脚本是否存在
    if ! remote_exec "cd '$REMOTE_APP_DIR' && [ -f './bin/startup.sh' ]" 5; then
        log_error "startup.sh脚本不存在: $REMOTE_APP_DIR/bin/startup.sh"
        exit 1
    fi

    # 确保脚本有执行权限
    remote_exec "chmod +x '$REMOTE_APP_DIR/bin/startup.sh'" 5

    # 启动服务
    log_info "执行startup.sh脚本启动服务"
    if remote_exec "cd '$REMOTE_APP_DIR' && ./bin/startup.sh" $STARTUP_TIMEOUT; then
        log_success "startup.sh脚本执行成功"
    else
        log_error "startup.sh脚本执行失败"
        exit 1
    fi

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 5

    # 检查进程是否启动成功
    local retry_count=0
    local max_retries=12  # 最多重试12次，每次5秒，总共60秒

    while [ $retry_count -lt $max_retries ]; do
        local java_pids=$(remote_exec "ps aux | grep 'xggl.*jar' | grep -v grep | awk '{print \$2}'" 5 || echo "")

        if [ -n "$java_pids" ]; then
            log_success "服务启动成功，进程ID: $java_pids"
            return 0
        fi

        retry_count=$((retry_count + 1))
        log_info "等待服务启动... ($retry_count/$max_retries)"
        sleep 5
    done

    log_error "服务启动超时，请检查服务日志"
    exit 1
}

# 步骤5: 部署验证
verify_deployment() {
    log_info "开始部署验证..."
    show_progress 5 5 "正在验证部署结果"

    # 检查进程状态
    log_info "检查Java进程状态"
    local java_pids=$(remote_exec "ps aux | grep 'xggl.*jar' | grep -v grep" 10 || echo "")

    if [ -z "$java_pids" ]; then
        log_error "验证失败：未找到运行中的Java进程"
        exit 1
    fi

    log_info "Java进程状态:"
    echo "$java_pids" | while read -r line; do
        log_info "  $line"
    done

    # 检查端口监听状态（假设应用监听8080端口，可根据实际情况调整）
    local app_port="8080"
    log_info "检查端口 $app_port 监听状态"

    if remote_exec "netstat -tlnp | grep ':$app_port '" 10 >/dev/null 2>&1; then
        log_success "端口 $app_port 正在监听"
    else
        log_warn "端口 $app_port 未在监听，可能应用还在启动中"
    fi

    # 检查应用日志（如果存在）
    local log_dir="$REMOTE_APP_DIR/logs"
    if remote_exec "[ -d '$log_dir' ]" 5; then
        log_info "检查应用日志"
        local latest_log=$(remote_exec "ls -t '$log_dir'/*.log 2>/dev/null | head -1" 5 || echo "")

        if [ -n "$latest_log" ]; then
            log_info "最新日志文件: $latest_log"
            local error_count=$(remote_exec "tail -100 '$latest_log' | grep -i error | wc -l" 5 || echo "0")

            if [ "$error_count" -gt 0 ]; then
                log_warn "日志中发现 $error_count 个错误，请检查应用日志"
            else
                log_success "日志检查正常，未发现错误"
            fi
        fi
    fi

    # 可选：HTTP健康检查（需要根据实际应用调整）
    local health_check_url="${HEALTH_CHECK_URL:-}"
    if [ -n "$health_check_url" ]; then
        log_info "执行HTTP健康检查: $health_check_url"

        local retry_count=0
        local max_retries=6  # 最多重试6次，每次10秒

        while [ $retry_count -lt $max_retries ]; do
            if remote_exec "curl -f -s -m 10 '$health_check_url' >/dev/null" 15; then
                log_success "HTTP健康检查通过"
                break
            fi

            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                log_info "健康检查失败，重试中... ($retry_count/$max_retries)"
                sleep 10
            else
                log_warn "HTTP健康检查失败，但进程正在运行"
            fi
        done
    fi

    log_success "部署验证完成"
}

# 清理函数
cleanup() {
    log_info "执行清理操作"

    # 清理本地临时文件（如果有的话）
    # 这里可以添加清理逻辑

    log_info "清理完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
自动化部署脚本 - sanyth-xuegong-5-server

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -c, --config FILE       指定配置文件
    --dry-run              模拟运行，不执行实际操作
    --skip-build           跳过Maven构建步骤
    --skip-backup          跳过备份步骤
    --force                强制执行，忽略警告

环境变量:
    DEPLOY_HOST            目标服务器IP地址
    DEPLOY_USER            SSH用户名
    DEPLOY_PORT            SSH端口（默认22）
    SSH_KEY                SSH私钥路径（默认~/.ssh/id_rsa）
    HEALTH_CHECK_URL       健康检查URL（可选）

示例:
    # 基本部署
    $0

    # 指定服务器信息
    DEPLOY_HOST=************* DEPLOY_USER=app $0

    # 跳过构建步骤
    $0 --skip-build

    # 模拟运行
    $0 --dry-run

EOF
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    local skip_build=false
    local skip_backup=false
    local dry_run=false
    local force=false
    local config_file=""

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--config)
                config_file="$2"
                shift 2
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --skip-build)
                skip_build=true
                shift
                ;;
            --skip-backup)
                skip_backup=true
                shift
                ;;
            --force)
                force=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 加载配置文件（如果指定）
    if [ -n "$config_file" ]; then
        if [ -f "$config_file" ]; then
            log_info "加载配置文件: $config_file"
            source "$config_file"
        else
            log_error "配置文件不存在: $config_file"
            exit 1
        fi
    fi

    # 显示脚本信息
    echo "============================================================================="
    echo "                    自动化部署脚本 - sanyth-xuegong-5-server"
    echo "============================================================================="
    echo "目标服务器: $SERVER_USER@$SERVER_HOST:$SERVER_PORT"
    echo "部署路径: $REMOTE_APP_DIR"
    echo "日志文件: $LOG_FILE"
    echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "============================================================================="

    if [ "$dry_run" = true ]; then
        log_info "模拟运行模式，不会执行实际操作"
        return 0
    fi

    # 确认部署操作
    if [ "$force" != true ]; then
        echo ""
        read -p "确认要执行部署操作吗？(y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "用户取消部署操作"
            exit 0
        fi
    fi

    # 记录开始时间
    local start_time=$(date +%s)

    # 执行部署步骤
    log_info "开始执行自动化部署流程"

    # 检查环境
    check_prerequisites

    # 步骤1: Maven构建
    if [ "$skip_build" != true ]; then
        maven_build
    else
        log_info "跳过Maven构建步骤"
        # 检查JAR文件是否存在
        if [ ! -f "$LOCAL_JAR_PATH" ]; then
            log_error "跳过构建但JAR文件不存在: $LOCAL_JAR_PATH"
            exit 1
        fi
    fi

    # 步骤2: 文件传输
    transfer_files

    # 步骤3: 停止服务
    stop_service

    # 步骤4: 启动服务
    start_service

    # 步骤5: 部署验证
    verify_deployment

    # 清理
    cleanup

    # 计算执行时间
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local minutes=$((duration / 60))
    local seconds=$((duration % 60))

    # 部署完成
    echo ""
    echo "============================================================================="
    log_success "自动化部署完成！"
    echo "============================================================================="
    echo "执行时间: ${minutes}分${seconds}秒"
    echo "日志文件: $LOG_FILE"
    echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "============================================================================="
}

# =============================================================================
# 脚本入口
# =============================================================================

# 检查是否以root用户运行（通常不建议）
if [ "$EUID" -eq 0 ]; then
    log_warn "检测到以root用户运行，建议使用普通用户执行部署脚本"
fi

# 设置脚本目录为工作目录
cd "$SCRIPT_DIR"

# 执行主函数
main "$@"
