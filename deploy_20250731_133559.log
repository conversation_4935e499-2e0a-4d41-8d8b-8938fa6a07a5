[2025-07-31 13:36:05] [INFO] 开始执行自动化部署流程
[2025-07-31 13:36:05] [INFO] 检查文件安全权限
[2025-07-31 13:36:05] [WARN] 脚本文件权限不安全 (711)，建议设置为600
[2025-07-31 13:36:05] [WARN] 执行命令: chmod 600 ./deploy.sh
[2025-07-31 13:36:06] [WARN] 继续使用当前权限，但存在安全风险
[2025-07-31 13:36:06] [INFO] 检查必要的工具和环境
[2025-07-31 13:36:06] [SUCCESS] 环境检查完成
[2025-07-31 13:36:06] [INFO] 跳过Maven构建步骤
[2025-07-31 13:36:06] [INFO] 开始文件传输...
[2025-07-31 13:36:06] [INFO] 测试SSH连接到 root@*************:22
[2025-07-31 13:36:06] [INFO] 使用配置的密码连接到 root@*************
[2025-07-31 13:36:09] [SUCCESS] SSH连接测试成功
[2025-07-31 13:36:09] [INFO] 创建远程备份目录
[2025-07-31 13:36:12] [INFO] 备份现有JAR文件为: xggl-1.0-SNAPSHOT-api.jar.backup.20250731_133612
[2025-07-31 13:36:14] [INFO] 传输JAR文件到目标服务器
Warning: Permanently added '*************' (ED25519) to the list of known hosts.

Authorized users only. All activities may be monitored and reported.
[2025-07-31 13:36:23] [SUCCESS] 文件传输成功
[2025-07-31 13:36:24] [SUCCESS] 远程JAR文件大小: 3.7M
[2025-07-31 13:36:24] [INFO] 开始停止服务...
[2025-07-31 13:36:24] [INFO] 尝试使用shutdown.sh脚本停止服务
[2025-07-31 13:36:32] [SUCCESS] 使用shutdown.sh成功停止服务
[2025-07-31 13:36:35] [INFO] 开始启动服务...
[2025-07-31 13:36:38] [INFO] 执行startup.sh脚本启动服务
[2025-07-31 13:36:40] [SUCCESS] startup.sh脚本执行成功
[2025-07-31 13:36:40] [INFO] 等待服务启动...
[2025-07-31 13:36:47] [INFO] 等待服务启动... (1/12)
[2025-07-31 13:36:54] [INFO] 等待服务启动... (2/12)
[2025-07-31 13:37:01] [INFO] 等待服务启动... (3/12)
[2025-07-31 13:37:07] [INFO] 等待服务启动... (4/12)
