# 自动化部署脚本使用说明

## 概述

本自动化部署脚本用于 `sanyth-xuegong-5-server` 项目的自动化部署，支持完整的构建、传输、停止、启动和验证流程。

## 功能特性

- ✅ **Maven自动构建**: 执行 `mvn clean package` 命令
- ✅ **安全文件传输**: 通过SCP传输JAR文件到目标服务器
- ✅ **智能服务管理**: 优雅停止和启动应用服务
- ✅ **完整错误处理**: 每个步骤都有错误检查和回滚机制
- ✅ **详细日志记录**: 完整的操作日志和进度显示
- ✅ **部署验证**: 自动验证部署结果
- ✅ **配置文件支持**: 支持外部配置文件
- ✅ **多环境支持**: 支持开发、测试、生产环境

## 文件结构

```
sanyth-xuegong-5-server/
├── deploy.sh              # 主部署脚本
├── deploy.conf            # 配置文件模板
├── DEPLOY_README.md       # 使用说明（本文件）
└── xggl-api/
    └── target/
        └── xggl-1.0-SNAPSHOT-api.jar  # 构建产物
```

## 快速开始

### 1. 准备工作

#### 1.1 环境要求
- Linux/macOS 操作系统
- Java 17+
- Maven 3.6+
- SSH客户端
- 目标服务器SSH访问权限

#### 1.2 SSH密钥配置
```bash
# 生成SSH密钥（如果没有）
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 将公钥复制到目标服务器
ssh-copy-id -i ~/.ssh/id_rsa.pub user@server_ip

# 测试SSH连接
ssh user@server_ip
```

#### 1.3 目标服务器准备
确保目标服务器上存在以下目录结构：
```
/home/<USER>/xuegong-v5/
├── bin/
│   ├── startup.sh         # 启动脚本
│   └── shutdown.sh        # 停止脚本
├── config/                # 配置文件目录
├── lib/                   # 依赖库目录
├── logs/                  # 日志目录
└── backup/                # 备份目录（自动创建）
```

### 2. 基本使用

#### 2.1 直接运行（使用环境变量）
```bash
# 设置环境变量
export DEPLOY_HOST="*************"
export DEPLOY_USER="app"
export SSH_KEY="~/.ssh/id_rsa"

# 执行部署
./deploy.sh
```

#### 2.2 使用配置文件
```bash
# 复制配置文件模板
cp deploy.conf my-deploy.conf

# 编辑配置文件
vim my-deploy.conf

# 使用配置文件部署
./deploy.sh -c my-deploy.conf
```

### 3. 高级用法

#### 3.1 命令行选项
```bash
# 显示帮助信息
./deploy.sh --help

# 模拟运行（不执行实际操作）
./deploy.sh --dry-run

# 跳过Maven构建
./deploy.sh --skip-build

# 强制执行（跳过确认）
./deploy.sh --force

# 组合使用
./deploy.sh -c prod.conf --force
```

#### 3.2 环境变量配置
```bash
# 服务器配置
export DEPLOY_HOST="**********"        # 目标服务器IP
export DEPLOY_USER="app"                # SSH用户名
export DEPLOY_PORT="22"                 # SSH端口
export SSH_KEY="~/.ssh/id_rsa"          # SSH私钥路径

# 可选配置
export HEALTH_CHECK_URL="http://localhost:8080/actuator/health"
```

## 配置说明

### 主要配置项

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `SERVER_HOST` | 目标服务器IP地址 | ************* | ✅ |
| `SERVER_USER` | SSH用户名 | app | ✅ |
| `SERVER_PORT` | SSH端口 | 22 | ❌ |
| `SSH_KEY` | SSH私钥路径 | ~/.ssh/id_rsa | ❌ |
| `REMOTE_APP_DIR` | 远程应用目录 | /home/<USER>/xuegong-v5 | ❌ |
| `SHUTDOWN_TIMEOUT` | 停止超时时间(秒) | 30 | ❌ |
| `STARTUP_TIMEOUT` | 启动超时时间(秒) | 60 | ❌ |

### 配置文件示例

#### 开发环境配置
```bash
SERVER_HOST="*************"
SERVER_USER="dev"
DEPLOY_ENV="dev"
IS_PRODUCTION=false
SHUTDOWN_TIMEOUT=15
STARTUP_TIMEOUT=30
```

#### 生产环境配置
```bash
SERVER_HOST="**********"
SERVER_USER="app"
DEPLOY_ENV="prod"
IS_PRODUCTION=true
PRODUCTION_SAFETY_CHECK=true
CREATE_BACKUP=true
SHUTDOWN_TIMEOUT=60
STARTUP_TIMEOUT=120
HEALTH_CHECK_URL="http://localhost:8080/actuator/health"
```

## 部署流程

### 执行步骤

1. **环境检查** - 验证必要工具和配置
2. **Maven构建** - 执行 `mvn clean package -DskipTests`
3. **文件传输** - 通过SCP传输JAR文件
4. **服务停止** - 优雅停止现有服务
5. **服务启动** - 启动新版本服务
6. **部署验证** - 验证部署结果

### 错误处理

- 每个步骤都有详细的错误检查
- 失败时自动停止执行并输出错误信息
- 支持查看详细日志进行问题排查

## 故障排除

### 常见问题

#### 1. SSH连接失败
```bash
# 检查SSH连接
ssh -v user@server_ip

# 检查SSH密钥权限
chmod 600 ~/.ssh/id_rsa

# 检查SSH配置
cat ~/.ssh/config
```

#### 2. Maven构建失败
```bash
# 手动执行构建查看详细错误
mvn clean package -DskipTests

# 检查Java版本
java -version
mvn -version
```

#### 3. 服务启动失败
```bash
# 检查远程服务器日志
ssh user@server_ip "tail -f /home/<USER>/xuegong-v5/logs/*.log"

# 检查端口占用
ssh user@server_ip "netstat -tlnp | grep 8080"
```

#### 4. 权限问题
```bash
# 确保脚本有执行权限
chmod +x deploy.sh

# 确保远程脚本有执行权限
ssh user@server_ip "chmod +x /home/<USER>/xuegong-v5/bin/*.sh"
```

### 日志分析

部署脚本会生成详细的日志文件：
```bash
# 查看最新部署日志
tail -f deploy_$(date +%Y%m%d)*.log

# 搜索错误信息
grep -i error deploy_*.log

# 查看特定步骤日志
grep "Maven构建" deploy_*.log
```

## 最佳实践

### 1. 安全建议
- 使用SSH密钥而非密码认证
- 定期更新SSH密钥
- 限制SSH用户权限
- 在生产环境启用所有安全检查

### 2. 部署建议
- 在非生产环境先测试
- 生产环境部署前创建备份
- 部署时间选择在业务低峰期
- 部署后进行充分的功能测试

### 3. 监控建议
- 配置应用健康检查
- 监控应用日志
- 设置部署通知
- 建立回滚机制

## 扩展功能

### 1. 通知集成
脚本支持多种通知方式：
- 邮件通知
- 钉钉机器人
- 企业微信机器人

### 2. 自定义钩子
支持在部署过程中执行自定义脚本：
- 部署前钩子
- 部署后钩子
- 失败处理钩子

### 3. 多环境管理
支持不同环境的配置管理：
```bash
# 开发环境
./deploy.sh -c config/dev.conf

# 测试环境
./deploy.sh -c config/test.conf

# 生产环境
./deploy.sh -c config/prod.conf
```

## 技术支持

如果在使用过程中遇到问题，请：

1. 查看部署日志文件
2. 检查配置是否正确
3. 验证网络连接和权限
4. 参考故障排除章节

---

**注意**: 请在生产环境使用前充分测试，确保所有配置正确无误。
