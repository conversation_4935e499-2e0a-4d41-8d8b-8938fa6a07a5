# SSH连接问题排查指南

## 概述

本文档提供了解决SSH连接问题的详细步骤和方法，特别是针对使用密码认证方式的自动化部署脚本。

## 快速诊断工具

我们提供了以下诊断工具帮助您快速排查SSH连接问题：

1. **快速测试脚本** (`test-ssh.sh`)：简单测试SSH连接是否正常
2. **详细诊断工具** (`ssh-diagnose.sh`)：提供全面的SSH连接诊断
3. **安装工具** (`install-sshpass.sh`)：安装SSH密码认证所需的sshpass工具

## 常见问题及解决方案

### 1. "SSH连接失败，请检查网络连接、用户名和密码"

#### 快速检查步骤

```bash
# 1. 运行快速测试脚本
./test-ssh.sh

# 2. 如果失败，运行详细诊断
./ssh-diagnose.sh --interactive
```

#### 常见原因及解决方案

**A. 密码错误**

检查 `deploy.sh` 文件中的密码配置：

```bash
# 编辑部署脚本
vim deploy.sh

# 找到并修改密码配置（第14-26行左右）
DEFAULT_SERVER_PASSWORD="your_actual_password"  # 修改为正确密码
```

**B. 服务器禁用了密码认证**

在服务器上检查SSH配置：

```bash
# 检查SSH配置
grep PasswordAuthentication /etc/ssh/sshd_config

# 如果显示 "PasswordAuthentication no"，需要修改为 yes
sudo sed -i 's/PasswordAuthentication no/PasswordAuthentication yes/' /etc/ssh/sshd_config
sudo systemctl restart sshd
```

**C. SSH服务未运行**

检查并启动SSH服务：

```bash
# 检查SSH服务状态
sudo systemctl status sshd

# 启动SSH服务
sudo systemctl start sshd
sudo systemctl enable sshd
```

**D. 防火墙阻止连接**

检查并配置防火墙：

```bash
# 检查防火墙状态
sudo ufw status
# 或
sudo firewall-cmd --list-all

# 允许SSH端口
sudo ufw allow 22
# 或
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --reload
```

**E. 网络连接问题**

检查网络连接：

```bash
# 测试网络连通性
ping 服务器IP

# 检查SSH端口是否开放
nc -zv 服务器IP 22
```

### 2. sshpass相关问题

**A. sshpass未安装**

```bash
# 安装sshpass
./install-sshpass.sh

# 或手动安装
# Ubuntu/Debian
sudo apt-get install sshpass
# CentOS/RHEL
sudo yum install sshpass
# macOS
brew install sshpass
```

**B. sshpass版本问题**

某些旧版本的sshpass可能存在兼容性问题：

```bash
# 检查sshpass版本
sshpass -V

# 更新sshpass
# Ubuntu/Debian
sudo apt-get update && sudo apt-get install --only-upgrade sshpass
# CentOS/RHEL
sudo yum update sshpass
# macOS
brew upgrade sshpass
```

### 3. 用户权限问题

**A. 用户不存在**

```bash
# 在服务器上检查用户是否存在
id 用户名

# 创建用户（如果不存在）
sudo useradd -m 用户名
sudo passwd 用户名
```

**B. 用户被锁定**

```bash
# 检查用户状态
sudo passwd -S 用户名

# 解锁用户
sudo passwd -u 用户名
```

**C. 用户没有足够权限**

```bash
# 检查用户权限
sudo -l -U 用户名

# 添加sudo权限（如果需要）
sudo usermod -aG sudo 用户名
```

## 手动测试SSH连接

### 基本测试

```bash
# 不使用密码（会提示输入）
ssh 用户名@服务器IP

# 使用密码
sshpass -p '密码' ssh 用户名@服务器IP

# 指定端口
sshpass -p '密码' ssh -p 端口号 用户名@服务器IP
```

### 详细模式测试

```bash
# 详细输出模式（查看详细连接过程）
sshpass -p '密码' ssh -v 用户名@服务器IP

# 更详细的输出（调试级别）
sshpass -p '密码' ssh -vvv 用户名@服务器IP
```

### 测试远程命令执行

```bash
# 执行简单命令
sshpass -p '密码' ssh 用户名@服务器IP 'echo "测试成功"'

# 执行多个命令
sshpass -p '密码' ssh 用户名@服务器IP 'whoami && pwd && ls -la'
```

## 服务器端配置检查

### SSH服务配置

检查并修改SSH服务配置：

```bash
# 查看SSH配置
sudo cat /etc/ssh/sshd_config

# 编辑SSH配置
sudo vim /etc/ssh/sshd_config

# 重要配置项
PasswordAuthentication yes       # 允许密码认证
PermitRootLogin yes/no           # 是否允许root登录
PubkeyAuthentication yes         # 允许密钥认证
AllowUsers user1 user2           # 允许的用户列表

# 重启SSH服务
sudo systemctl restart sshd
```

### 日志检查

检查SSH服务日志：

```bash
# 查看SSH日志
sudo tail -f /var/log/auth.log   # Debian/Ubuntu
sudo tail -f /var/log/secure     # CentOS/RHEL

# 查看系统日志
sudo journalctl -u sshd          # 使用systemd的系统
```

## 高级排查技巧

### 1. 临时禁用StrictHostKeyChecking

```bash
sshpass -p '密码' ssh -o StrictHostKeyChecking=no 用户名@服务器IP
```

### 2. 增加连接超时时间

```bash
sshpass -p '密码' ssh -o ConnectTimeout=30 用户名@服务器IP
```

### 3. 使用不同的SSH客户端

如果系统SSH客户端有问题，可以尝试其他客户端：

```bash
# 使用PuTTY（Windows）
putty -ssh 用户名@服务器IP

# 使用其他SSH客户端
```

### 4. 检查SELinux（CentOS/RHEL系统）

```bash
# 检查SELinux状态
getenforce

# 临时禁用SELinux
sudo setenforce 0

# 永久禁用SELinux
sudo sed -i 's/SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config
```

## 常见错误消息及解决方案

### "Permission denied (publickey,password)"

- 原因：密码错误或服务器不接受密码认证
- 解决方案：检查密码或启用密码认证

### "Connection refused"

- 原因：SSH服务未运行或防火墙阻止
- 解决方案：启动SSH服务或配置防火墙

### "Connection timed out"

- 原因：网络问题或防火墙阻止
- 解决方案：检查网络连接和防火墙配置

### "Host key verification failed"

- 原因：服务器主机密钥变更
- 解决方案：使用 `-o StrictHostKeyChecking=no` 或更新known_hosts

### "No route to host"

- 原因：网络路由问题
- 解决方案：检查网络配置和路由表

## 总结

SSH连接问题通常可以通过以下步骤解决：

1. 使用提供的诊断工具快速定位问题
2. 检查密码配置是否正确
3. 确认服务器允许密码认证
4. 检查网络连接和防火墙配置
5. 检查用户权限和SSH服务状态

如果以上步骤都无法解决问题，请考虑使用SSH密钥认证方式，这通常更可靠且更安全。
