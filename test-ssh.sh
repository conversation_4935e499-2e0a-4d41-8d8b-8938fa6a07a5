#!/bin/bash

# =============================================================================
# 快速SSH连接测试脚本
# 用于快速测试SSH密码认证是否正常工作
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 从deploy.sh读取配置
if [ -f "deploy.sh" ]; then
    # 提取配置信息
    DEFAULT_SERVER_PASSWORD=$(grep "DEFAULT_SERVER_PASSWORD=" deploy.sh | cut -d'"' -f2)
    SERVER_HOST=$(grep "SERVER_HOST=" deploy.sh | head -1 | cut -d'"' -f2)
    SERVER_USER=$(grep "SERVER_USER=" deploy.sh | head -1 | cut -d'"' -f2)
    SERVER_PORT=$(grep "SERVER_PORT=" deploy.sh | head -1 | cut -d'"' -f2)
    
    # 使用环境变量或默认值
    SERVER_HOST="${DEPLOY_HOST:-$SERVER_HOST}"
    SERVER_USER="${DEPLOY_USER:-$SERVER_USER}"
    SERVER_PORT="${DEPLOY_PORT:-$SERVER_PORT}"
    SERVER_PASSWORD="${DEPLOY_PASSWORD:-$DEFAULT_SERVER_PASSWORD}"
else
    echo -e "${RED}错误: 未找到deploy.sh文件${NC}"
    exit 1
fi

echo "============================================================================="
echo "                           快速SSH连接测试"
echo "============================================================================="
echo "目标服务器: $SERVER_USER@$SERVER_HOST:$SERVER_PORT"
echo "============================================================================="

# 检查配置
if [ -z "$SERVER_PASSWORD" ] || [ "$SERVER_PASSWORD" = "your_server_password_here" ]; then
    echo -e "${RED}错误: 请先在deploy.sh中配置正确的密码${NC}"
    echo "编辑 deploy.sh 文件，修改 DEFAULT_SERVER_PASSWORD 变量"
    exit 1
fi

# 检查sshpass
if ! command -v sshpass >/dev/null 2>&1; then
    echo -e "${RED}错误: sshpass未安装${NC}"
    echo "请运行: ./install-sshpass.sh"
    exit 1
fi

echo ""
echo "1. 测试基本SSH连接..."
echo "执行命令: sshpass -p '***' ssh -o ConnectTimeout=10 $SERVER_USER@$SERVER_HOST 'echo 连接成功'"

if sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
    -o ConnectTimeout=10 \
    -o StrictHostKeyChecking=no \
    -o UserKnownHostsFile=/dev/null \
    -o LogLevel=QUIET \
    "$SERVER_USER@$SERVER_HOST" "echo '连接成功'" 2>/dev/null; then
    echo -e "${GREEN}✓ SSH连接测试成功！${NC}"
    echo ""
    
    echo "2. 测试远程命令执行..."
    echo "执行命令: whoami && pwd && date"
    echo "----------------------------------------"
    sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
        -o ConnectTimeout=10 \
        -o StrictHostKeyChecking=no \
        -o UserKnownHostsFile=/dev/null \
        -o LogLevel=QUIET \
        "$SERVER_USER@$SERVER_HOST" "whoami && pwd && date"
    echo "----------------------------------------"
    echo -e "${GREEN}✓ 远程命令执行成功！${NC}"
    echo ""
    
    echo "3. 测试目标目录..."
    REMOTE_APP_DIR="/home/<USER>/xuegong-v5"
    if sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
        -o ConnectTimeout=10 \
        -o StrictHostKeyChecking=no \
        -o UserKnownHostsFile=/dev/null \
        -o LogLevel=QUIET \
        "$SERVER_USER@$SERVER_HOST" "[ -d '$REMOTE_APP_DIR' ]" 2>/dev/null; then
        echo -e "${GREEN}✓ 目标目录 $REMOTE_APP_DIR 存在${NC}"
        
        # 检查启动和停止脚本
        if sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
            -o ConnectTimeout=10 \
            -o StrictHostKeyChecking=no \
            -o UserKnownHostsFile=/dev/null \
            -o LogLevel=QUIET \
            "$SERVER_USER@$SERVER_HOST" "[ -f '$REMOTE_APP_DIR/bin/startup.sh' ]" 2>/dev/null; then
            echo -e "${GREEN}✓ 启动脚本存在${NC}"
        else
            echo -e "${YELLOW}⚠ 启动脚本不存在: $REMOTE_APP_DIR/bin/startup.sh${NC}"
        fi
        
        if sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
            -o ConnectTimeout=10 \
            -o StrictHostKeyChecking=no \
            -o UserKnownHostsFile=/dev/null \
            -o LogLevel=QUIET \
            "$SERVER_USER@$SERVER_HOST" "[ -f '$REMOTE_APP_DIR/bin/shutdown.sh' ]" 2>/dev/null; then
            echo -e "${GREEN}✓ 停止脚本存在${NC}"
        else
            echo -e "${YELLOW}⚠ 停止脚本不存在: $REMOTE_APP_DIR/bin/shutdown.sh${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ 目标目录 $REMOTE_APP_DIR 不存在${NC}"
        echo "请确保目标服务器上的目录结构正确"
    fi
    
    echo ""
    echo "============================================================================="
    echo -e "${GREEN}SSH连接测试完成！所有测试都通过了。${NC}"
    echo "现在可以运行部署脚本: ./deploy.sh"
    echo "============================================================================="
    
else
    echo -e "${RED}✗ SSH连接失败${NC}"
    echo ""
    echo "可能的原因："
    echo "1. 密码错误"
    echo "2. 服务器禁用了密码认证"
    echo "3. 网络连接问题"
    echo "4. SSH服务未运行"
    echo ""
    echo "建议的排查步骤："
    echo "1. 检查密码是否正确"
    echo "2. 运行详细诊断: ./ssh-diagnose.sh --interactive"
    echo "3. 手动测试连接: sshpass -p 'your_password' ssh -v $SERVER_USER@$SERVER_HOST"
    echo ""
    
    # 尝试详细的错误诊断
    echo "正在进行详细错误诊断..."
    echo "----------------------------------------"
    sshpass -p "$SERVER_PASSWORD" ssh -v -p "$SERVER_PORT" \
        -o ConnectTimeout=10 \
        -o StrictHostKeyChecking=no \
        -o UserKnownHostsFile=/dev/null \
        "$SERVER_USER@$SERVER_HOST" "echo '连接成功'" 2>&1 | head -20
    echo "----------------------------------------"
    
    exit 1
fi
