#!/bin/bash

# =============================================================================
# SSH连接诊断工具
# 用于诊断和解决SSH连接问题
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $*"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $*"
}

log_result() {
    echo -e "${CYAN}[RESULT]${NC} $*"
}

# 显示帮助信息
show_help() {
    cat << EOF
SSH连接诊断工具

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -H, --host HOST         目标服务器IP地址
    -u, --user USER         SSH用户名
    -p, --port PORT         SSH端口 (默认: 22)
    -P, --password PASS     SSH密码
    --interactive           交互式输入连接信息

示例:
    $0 -H ************* -u app -P mypassword
    $0 --interactive
    $0 --host ************* --user app --port 22

EOF
}

# 交互式收集连接信息
collect_connection_info() {
    echo "============================================================================="
    echo "                           SSH连接诊断工具"
    echo "============================================================================="
    echo ""
    
    read -p "服务器IP地址: " SERVER_HOST
    while [ -z "$SERVER_HOST" ]; do
        log_error "服务器IP不能为空"
        read -p "服务器IP地址: " SERVER_HOST
    done
    
    read -p "SSH用户名 [app]: " SERVER_USER
    SERVER_USER=${SERVER_USER:-app}
    
    read -p "SSH端口 [22]: " SERVER_PORT
    SERVER_PORT=${SERVER_PORT:-22}
    
    echo -n "SSH密码: "
    read -s SERVER_PASSWORD
    echo ""
    
    while [ -z "$SERVER_PASSWORD" ]; do
        log_error "密码不能为空"
        echo -n "SSH密码: "
        read -s SERVER_PASSWORD
        echo ""
    done
}

# 检查必要工具
check_tools() {
    log_step "检查必要工具..."
    
    local missing_tools=()
    
    if ! command -v sshpass >/dev/null 2>&1; then
        missing_tools+=("sshpass")
    fi
    
    if ! command -v ssh >/dev/null 2>&1; then
        missing_tools+=("ssh")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_error "请先安装缺少的工具"
        exit 1
    fi
    
    log_result "工具检查通过"
}

# 测试网络连通性
test_network() {
    log_step "测试网络连通性..."
    
    if ping -c 3 -W 3 "$SERVER_HOST" >/dev/null 2>&1; then
        log_result "✓ 网络连通性正常"
        return 0
    else
        log_result "✗ 网络连通性测试失败"
        log_warn "这可能是因为目标服务器禁用了ICMP响应"
        return 1
    fi
}

# 测试端口连通性
test_port() {
    log_step "测试SSH端口 $SERVER_PORT 连通性..."
    
    if command -v nc >/dev/null 2>&1; then
        if nc -z -w 5 "$SERVER_HOST" "$SERVER_PORT" 2>/dev/null; then
            log_result "✓ SSH端口 $SERVER_PORT 可访问"
            return 0
        else
            log_result "✗ SSH端口 $SERVER_PORT 不可访问"
            log_error "可能的原因："
            log_error "  - SSH服务未运行"
            log_error "  - 防火墙阻止了端口 $SERVER_PORT"
            log_error "  - 端口配置错误"
            return 1
        fi
    elif command -v telnet >/dev/null 2>&1; then
        if timeout 5 telnet "$SERVER_HOST" "$SERVER_PORT" </dev/null 2>&1 | grep -q "Connected"; then
            log_result "✓ SSH端口 $SERVER_PORT 可访问"
            return 0
        else
            log_result "✗ SSH端口 $SERVER_PORT 不可访问"
            return 1
        fi
    else
        log_warn "nc和telnet命令都不可用，跳过端口测试"
        return 0
    fi
}

# 测试SSH连接（基本）
test_ssh_basic() {
    log_step "测试基本SSH连接..."
    
    local output
    output=$(sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
        -o ConnectTimeout=10 \
        -o BatchMode=yes \
        -o StrictHostKeyChecking=no \
        -o UserKnownHostsFile=/dev/null \
        -o LogLevel=QUIET \
        "$SERVER_USER@$SERVER_HOST" "echo 'SSH连接成功'" 2>&1)
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_result "✓ SSH连接成功"
        return 0
    else
        log_result "✗ SSH连接失败 (退出码: $exit_code)"
        echo "错误输出: $output"
        return 1
    fi
}

# 测试SSH连接（详细模式）
test_ssh_verbose() {
    log_step "测试SSH连接（详细模式）..."
    
    echo "执行命令:"
    echo "sshpass -p '***' ssh -v -p $SERVER_PORT -o ConnectTimeout=10 $SERVER_USER@$SERVER_HOST"
    echo ""
    echo "详细输出:"
    echo "----------------------------------------"
    
    sshpass -p "$SERVER_PASSWORD" ssh -v -p "$SERVER_PORT" \
        -o ConnectTimeout=10 \
        -o StrictHostKeyChecking=no \
        -o UserKnownHostsFile=/dev/null \
        "$SERVER_USER@$SERVER_HOST" "echo 'SSH详细连接测试成功'" 2>&1
    
    local exit_code=$?
    echo "----------------------------------------"
    echo "退出码: $exit_code"
}

# 检查服务器SSH配置
check_server_config() {
    log_step "尝试检查服务器SSH配置..."
    
    local config_output
    config_output=$(sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
        -o ConnectTimeout=10 \
        -o StrictHostKeyChecking=no \
        -o UserKnownHostsFile=/dev/null \
        -o LogLevel=QUIET \
        "$SERVER_USER@$SERVER_HOST" \
        "grep -E '^(PasswordAuthentication|PermitRootLogin|PubkeyAuthentication)' /etc/ssh/sshd_config 2>/dev/null || echo 'Cannot read sshd_config'" 2>&1)
    
    if [ $? -eq 0 ] && [ "$config_output" != "Cannot read sshd_config" ]; then
        log_result "服务器SSH配置:"
        echo "$config_output"
    else
        log_warn "无法读取服务器SSH配置文件"
    fi
}

# 提供解决建议
provide_suggestions() {
    echo ""
    log_step "常见问题解决建议:"
    echo ""
    
    echo "1. 密码认证被禁用:"
    echo "   在服务器上编辑 /etc/ssh/sshd_config"
    echo "   确保包含: PasswordAuthentication yes"
    echo "   然后重启SSH服务: sudo systemctl restart sshd"
    echo ""
    
    echo "2. 防火墙问题:"
    echo "   检查服务器防火墙: sudo ufw status"
    echo "   允许SSH端口: sudo ufw allow $SERVER_PORT"
    echo ""
    
    echo "3. SSH服务未运行:"
    echo "   检查SSH服务状态: sudo systemctl status sshd"
    echo "   启动SSH服务: sudo systemctl start sshd"
    echo ""
    
    echo "4. 用户权限问题:"
    echo "   检查用户是否存在: id $SERVER_USER"
    echo "   检查用户是否被锁定: sudo passwd -S $SERVER_USER"
    echo ""
    
    echo "5. 手动测试命令:"
    echo "   sshpass -p 'your_password' ssh -v $SERVER_USER@$SERVER_HOST"
    echo "   ssh -v $SERVER_USER@$SERVER_HOST  # 不使用密码"
}

# 主函数
main() {
    local interactive=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -H|--host)
                SERVER_HOST="$2"
                shift 2
                ;;
            -u|--user)
                SERVER_USER="$2"
                shift 2
                ;;
            -p|--port)
                SERVER_PORT="$2"
                shift 2
                ;;
            -P|--password)
                SERVER_PASSWORD="$2"
                shift 2
                ;;
            --interactive)
                interactive=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果是交互模式或缺少参数，则收集连接信息
    if [ "$interactive" = true ] || [ -z "$SERVER_HOST" ] || [ -z "$SERVER_USER" ] || [ -z "$SERVER_PASSWORD" ]; then
        collect_connection_info
    fi
    
    # 设置默认值
    SERVER_PORT=${SERVER_PORT:-22}
    
    echo ""
    echo "============================================================================="
    echo "开始SSH连接诊断"
    echo "目标: $SERVER_USER@$SERVER_HOST:$SERVER_PORT"
    echo "============================================================================="
    
    # 执行诊断步骤
    check_tools
    echo ""
    
    test_network
    echo ""
    
    test_port
    echo ""
    
    test_ssh_basic
    echo ""
    
    if [ $? -ne 0 ]; then
        log_warn "基本SSH连接失败，运行详细诊断..."
        echo ""
        test_ssh_verbose
        echo ""
    fi
    
    check_server_config
    echo ""
    
    provide_suggestions
    
    echo "============================================================================="
    echo "诊断完成"
    echo "============================================================================="
}

# 执行主函数
main "$@"
