package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.form.*;
import com.sanythadmin.project.leave.query.LeaveQuerySuper;
import com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository;
import com.sanythadmin.project.leave.service.LeaveStudentActionService;
import com.sanythadmin.project.leave.vo.LeaveStudentActionDetailVo;
import com.sanythadmin.project.leave.vo.LeaveStudentActionLeavePageVo;
import com.sanythadmin.project.leave.vo.LeaveStudentActionProjectPageVo;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.NoSuchElementException;

/**
 * 请假模块/学生操作
 *
 * @since 2025/5/8 9:29
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/student_action")
public class LeaveStudentActionController {

    private final LeaveStudentActionService leaveStudentActionService;
    private final LeaveProjectAreaRepository projectAreaRepository;

    @ExceptionHandler(NoSuchElementException.class)
    public ApiResult<Object> handleNoSuchElementException(Exception ex) {
        return new ApiResult<>(1, "未找到数据");
    }

    /**
     * 可选请假类型-分页
     * 权限标识：leave:student_action:list
     */
    @PreAuthorize("hasAuthority('leave:student_action:list')")
    @GetMapping("optional_project_page")
    public LeaveStudentActionProjectPageVo optionalProjectPage(String lastId, @RequestParam(required = false, defaultValue = "10") Integer limit) {
        return leaveStudentActionService.optionalProjectPage(lastId, limit);
    }

    /**
     * 可选请假类型-全部
     * 权限标识：leave:student_action:list
     */
    @PreAuthorize("hasAuthority('leave:student_action:list')")
    @GetMapping("optional_project_all")
    public List<LeaveIdTextPairForm> optionalProjectAll() {
        return leaveStudentActionService.optionalProjectAll();
    }

    /**
     * 表单字段
     * 权限标识：leave:student_action:list
     */
    @PreAuthorize("hasAuthority('leave:student_action:list')")
    @GetMapping("form_fields_for_add")
    public List<LeaveApplicationFieldForm> formFieldsForAdd(String projectId, LeaveRequestType requestType) {
        return leaveStudentActionService.formFieldsForAdd(projectId, requestType);
    }

    /**
     * 申请提交
     * 权限标识：leave:student_action:operation
     */
    @PreAuthorize("hasAuthority('leave:student_action:operation')")
//    @PostMapping("application_submit")
    @PostMapping(path = "/application_submit", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public void applicationSubmit(@ModelAttribute LeaveApplicationForm form) {
        leaveStudentActionService.applicationSubmit(form);
    }

    /**
     * 请假申请列表
     * 权限标识：leave:student_action:list
     */
    @PreAuthorize("hasAuthority('leave:student_action:list')")
    @GetMapping("leave_page")
    public PageResult<LeaveStudentActionLeavePageVo> leavePage(LeaveQuerySuper query) {
        return leaveStudentActionService.leavePage(query);
    }

    /**
     * 请假申请详情
     * 权限标识：leave:student_action:list
     */
    @PreAuthorize("hasAuthority('leave:student_action:list')")
    @GetMapping("leave_detail")
    public LeaveStudentActionDetailVo leaveDetail(@RequestParam String leaveId) {
        return leaveStudentActionService.leaveDetail(leaveId);
    }

    /**
     * 删除
     * 权限标识：leave:student_action:remove
     */
    @PreAuthorize("hasAuthority('leave:student_action:remove')")
    @PostMapping("delete/{id}")
    public void deleteById(@PathVariable String id) {
        leaveStudentActionService.deleteById(id);
    }

    /**
     * 撤回
     * 权限标识：leave:student_action:operation
     */
    @PreAuthorize("hasAuthority('leave:student_action:operation')")
    @PostMapping("withdraw_submit")
    public void withdrawSubmit(@RequestBody LeaveWithdrawForm form) {
        leaveStudentActionService.onWithdraw(form);
    }

    /**
     * 地理范围
     * 权限标识：leave:student_action:list
     */
    @PreAuthorize("hasAuthority('leave:student_action:list')")
    @GetMapping("location_range")
    public List<LeaveProjectAreaForm> locationRange(@RequestParam String projectId) {
        return projectAreaRepository.findAllByProjectId(projectId).stream().map(LeaveProjectAreaForm::new).toList();
    }
}
