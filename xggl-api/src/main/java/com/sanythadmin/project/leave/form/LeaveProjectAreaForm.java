package com.sanythadmin.project.leave.form;

import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.leave.entity.LeaveProjectArea;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/5/19 11:48
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveProjectAreaForm extends LeaveBaseForm {

    public LeaveProjectAreaForm(LeaveProjectArea entity) {
        super(entity);

//        this.projectId = entity.getProject().getId();
        this.name = entity.getName();
        this.latitude = entity.getLatitude();
        this.longitude = entity.getLongitude();
        this.radius = entity.getRadius();
        this.detail = entity.getDetail();
    }

    /**
     * 类型ID
     */
    private String projectId;
    private String name;
    /**
     * 经度
     */
    private Double latitude;
    /**
     * 纬度
     */
    private Double longitude;
    /**
     * 半径（米）
     */
    private Integer radius;
    /**
     * 详细信息
     */
    private String detail;

    // 引用的预设地址 ID
    private String presetAddressId;
    // 是否将本次配置同步到预设地址列表
    private JudgeMark syncToPresetAddress;

}
