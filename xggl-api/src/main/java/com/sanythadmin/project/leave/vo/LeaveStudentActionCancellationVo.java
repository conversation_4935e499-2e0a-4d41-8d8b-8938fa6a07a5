package com.sanythadmin.project.leave.vo;

import com.sanythadmin.project.leave.entity.LeaveApplication;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @since 2025/7/21 10:33
 */
@NoArgsConstructor
@Data
public class LeaveStudentActionCancellationVo extends LeaveRecordCancellationVo {

    public LeaveStudentActionCancellationVo(LeaveApplication application) {
        super(application);
    }

    private String address;
    private Double longitude;
    private Double latitude;

//    /**
//     * 是否可以删除
//     */
//    public boolean isCanDelete() {
//        return LeaveUtil.applicationCanDelete(application);
//    }

}
