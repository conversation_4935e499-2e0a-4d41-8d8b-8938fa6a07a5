package com.sanythadmin.project.leave.repository;

import com.sanythadmin.project.leave.entity.LeaveProject;
import com.sanythadmin.project.leave.entity.LeaveProjectArea;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface LeaveProjectAreaRepository extends PagingAndSortingRepository<LeaveProjectArea, String>, CrudRepository<LeaveProjectArea, String>, JpaSpecificationExecutor<LeaveProjectArea> {

    Iterable<LeaveProjectArea> findAllByProject(LeaveProject project);

    @Query("select a from LeaveProjectArea a where a.project.id = :projectId")
    List<LeaveProjectArea> findAllByProjectId(String projectId);
}
