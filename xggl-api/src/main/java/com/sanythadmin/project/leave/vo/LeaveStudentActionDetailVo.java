package com.sanythadmin.project.leave.vo;

import com.sanythadmin.project.leave.entity.LeaveApplication;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @since 2025/6/6 10:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveStudentActionDetailVo extends LeaveStudentActionLeavePageVo {

    public LeaveStudentActionDetailVo(LeaveApplication application) {
        super(application);
    }

    /**
     * 仅允许在地理范围内申请销假
     */
    private boolean cancellationApplyInArea;

    private String address;
    private Double longitude;
    private Double latitude;

    /**
     * 学生基本信息
     */
    private List<LeaveNameValuePairVo> basicInfo;
    /**
     * 申请字段
     */
    private List<LeaveApplicationFieldVo> applicationFields;
    /**
     * 流程信息
     */
    private List<LeaveApplicationFlowVo> flows;
    /**
     * 销假记录
     */
    private List<LeaveStudentActionCancellationVo> cancellations;

}
