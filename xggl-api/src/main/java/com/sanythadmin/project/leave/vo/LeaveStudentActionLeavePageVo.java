package com.sanythadmin.project.leave.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.time.LocalDateTime;

/**
 * @since 2025/6/19 10:11
 */
@NoArgsConstructor
@Data
public class LeaveStudentActionLeavePageVo {

    public LeaveStudentActionLeavePageVo(LeaveApplication application) {
        this.application = application;

        this.id = application.getId();
        this.project = new LeaveIdTextPairForm(application.getProject().getId(), application.getProject().getDisplayName());
        this.startTime = application.getStartTime();
        this.endTime = application.getEndTime();
        this.approvalStartTime = application.getApprovalStartTime();
        this.approvalEndTime = application.getApprovalEndTime();
        this.approved = application.getApproved();
        this.submitTime = application.getSubmitTime();
        this.createdAt = application.getCreatedAt();
        this.durationDays = application.getDurationDays();
        this.withdrawAt = application.getWithdrawAt();
    }

    @JsonIgnore
    private LeaveApplication application;
    @JsonIgnore
    private LeaveApplication latestCancelledApplication;
    private String id;
    private LeaveIdTextPairForm project;
    /**
     * yyyy-MM-dd HH:mm
     */
    private LocalDateTime startTime;
    /**
     * yyyy-MM-dd HH:mm
     */
    private LocalDateTime endTime;
    /**
     * 审批开始时间
     */
    private LocalDateTime approvalStartTime;
    private LocalDateTime approvalEndTime;
    /**
     * 是否审批通过
     */
    private Boolean approved;
    private LocalDateTime submitTime;
    private LocalDateTime createdAt;
    private Integer durationDays;
    /**
     * 撤回时间
     */
    private LocalDateTime withdrawAt;

    /**
     * 是否可以删除
     */
    public boolean isCanDelete() {
        return LeaveUtil.applicationCanDelete(application);
    }

    /**
     * 是否可撤回
     */
    public boolean isCanWithdraw() {
        return LeaveUtil.applicationCanWithdraw(application);
    }

    /**
     * 可以申请销假
     */
    public boolean isCanApplyCancel() {
        if (application.getRequestType() != LeaveRequestType.LEAVE) {
            return false;
        }
        if (BooleanUtils.toBoolean(application.getCancellationAfterLeaveEnd())) {
            return false;
        }
//        return application.getApprovalEndTime() != null && application.getApproved() && application.getCancelledTime() == null;
        if (application.getApprovalEndTime() == null) {
            return false;
        }
        if (BooleanUtils.isNotTrue(application.getApproved())) {
            return false;
        }
        if (application.getCancelledTime() != null) {
            return false;
        }
        return latestCancelledApplication == null || latestCancelledApplication.getApprovalEndTime() != null;
    }

    /**
     * 已销假
     */
    public boolean isCancelled() {
        return LeaveUtil.applicationCancelled(application);
    }

    public String getStatus() {
        return LeaveUtil.getLeaveApplicationStatus(application);
    }

    public String getCancellationApprovalStatus() {
        return LeaveUtil.getCancellationApprovalStatus(latestCancelledApplication);
    }

}
