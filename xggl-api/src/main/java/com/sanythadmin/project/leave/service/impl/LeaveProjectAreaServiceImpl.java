package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.enums.State;
import com.sanythadmin.project.checkin.entity.CheckinAddress;
import com.sanythadmin.project.checkin.service.CheckinAddressRealService;
import com.sanythadmin.project.leave.entity.LeaveProjectArea;
import com.sanythadmin.project.leave.form.LeaveProjectAreaForm;
import com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository;
import com.sanythadmin.project.leave.repository.LeaveProjectRepository;
import com.sanythadmin.project.leave.service.LeaveProjectAreaService;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * @since 2025/6/4 15:34
 */
@AllArgsConstructor
@Service
public class LeaveProjectAreaServiceImpl implements LeaveProjectAreaService {

    private final LeaveProjectAreaRepository locationRepository;
    private final CheckinAddressRealService checkinAddressRealService;
    private final LeaveProjectRepository projectRepository;

    @Transactional
    @Override
    public void operation(LeaveProjectAreaForm form) {
        LeaveProjectArea entity;
        if (form.getId() != null) {
            entity = locationRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new LeaveProjectArea();
            entity.setCreatedAt(LocalDateTime.now());
            entity.setProject(projectRepository.findById(form.getProjectId()).orElseThrow(() -> new RuntimeException("此类型不存在")));

            // 引用的预设地址
            if (StringUtils.isNotBlank(form.getPresetAddressId())) {
                CheckinAddress address = checkinAddressRealService.getById(form.getPresetAddressId());
                form.setName(address.getName());
                String jwdzb = address.getJwdzb();
                String[] split = jwdzb.split("#");
                form.setLongitude(Double.valueOf(split[0]));
                form.setLatitude(Double.valueOf(split[1]));
                form.setRadius(address.getFwbj());
                form.setDetail(address.getBz());
            }
        }

        LeaveUtil.checkTrue(StringUtils.isNotBlank(form.getName()), "名称不能为空");
        LeaveUtil.checkTrue(form.getLongitude() != null && form.getLatitude() != null, "经纬度不能为空");
        LeaveUtil.checkTrue(form.getRadius() != null, "半径不能为空");
        entity.setName(form.getName());
        entity.setLongitude(form.getLongitude());
        entity.setLatitude(form.getLatitude());
        entity.setRadius(form.getRadius());
        entity.setDetail(form.getDetail());
        locationRepository.save(entity);

        if (StringUtils.isEmpty(form.getPresetAddressId()) && LeaveUtil.judgeMarkToBoolean(form.getSyncToPresetAddress())) {
            CheckinAddress checkinAddress = new CheckinAddress();
            checkinAddress.setName(entity.getName());
            checkinAddress.setJwdzb(entity.getLongitude() + "#" + entity.getLatitude());
            checkinAddress.setFwbj(entity.getRadius());
            checkinAddress.setBz(entity.getDetail());
            checkinAddress.setStatus(State.ENABLED);
            checkinAddress.setType("poi");
            checkinAddressRealService.serviceSave(checkinAddress);
        }
    }
}
