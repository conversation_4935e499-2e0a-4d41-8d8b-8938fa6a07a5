package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import com.sanythadmin.project.workstudy.enums.AttendanceType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 勤工助学考勤记录查询参数
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxAttendanceRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学生申请ID
     */
    @QueryField(type = QueryType.EQ)
    private String studentApplyId;

    /**
     * 岗位ID（用于查询条件，通过关联查询）
     */
    @QueryField(ignore = true)
    private String jobId;

    /**
     * 学号（用于查询条件，通过关联查询）
     */
    @QueryField(ignore = true)
    private String xgh;

    /**
     * 学年学期（用于查询条件，通过关联查询）
     */
    @QueryField(ignore = true)
    private String xnxq;

    /**
     * 考勤日期开始
     */
    @QueryField(ignore = true)
    private LocalDate attendanceDateStart;

    /**
     * 考勤日期结束  
     */
    @QueryField(ignore = true)
    private LocalDate attendanceDateEnd;

    /**
     * 考勤类型
     */
    @QueryField(type = QueryType.EQ)
    private AttendanceType attendanceType;

    /**
     * 是否正常
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark isNormal;

    /**
     * 打卡状态
     */
    @QueryField(type = QueryType.EQ)
    private AttendanceStatus attendanceStatus;

    /**
     * 学生姓名（用于查询）
     */
    @QueryField(ignore = true)
    private String xm;

    /**
     * 岗位名称（用于查询）
     */
    @QueryField(ignore = true)
    private String jobName;

    /**
     * 用人单位ID（用于用人单位查询自己的考勤记录）
     */
    @QueryField(ignore = true)
    private String employerId;

    /**
     * 是否按学生分组查询（true：返回学生分组统计数据，false：返回详细考勤记录）
     */
    @QueryField(ignore = true)
    private Boolean groupByStudent;
}
