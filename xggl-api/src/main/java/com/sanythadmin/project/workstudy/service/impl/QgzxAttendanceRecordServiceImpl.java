package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.LocalDateQueryUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workstudy.entity.QgzxAttendanceRecord;
import com.sanythadmin.project.workstudy.entity.QgzxEmployer;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import com.sanythadmin.project.workstudy.enums.AttendanceType;
import com.sanythadmin.project.workstudy.enums.WorkStatus;
import com.sanythadmin.project.workstudy.mapper.QgzxAttendanceRecordMapper;
import com.sanythadmin.project.workstudy.param.QgzxAttendanceRecordParam;
import com.sanythadmin.project.workstudy.service.QgzxAttendanceRecordService;
import com.sanythadmin.project.workstudy.service.QgzxAttendanceStatusService;
import com.sanythadmin.project.workstudy.service.QgzxEmployerService;
import com.sanythadmin.project.workstudy.service.QgzxJobApplicationService;
import com.sanythadmin.project.workstudy.service.QgzxStudentApplyService;
import com.sanythadmin.project.workstudy.vo.QgzxAttendanceRecordVO;
import com.sanythadmin.project.workstudy.vo.QgzxStudentAttendanceStatVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Comparator;

/**
 * 勤工助学考勤记录Service实现
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QgzxAttendanceRecordServiceImpl extends ServiceImpl<QgzxAttendanceRecordMapper, QgzxAttendanceRecord> implements QgzxAttendanceRecordService {

    private final QgzxAttendanceRecordMapper mapper;
    private final QgzxStudentApplyService studentApplyService;
    private final QgzxJobApplicationService jobApplicationService;
    private final QgzxEmployerService employerService;
    private final QgzxAttendanceStatusService attendanceStatusService;

    @Override
    public PageResult<QgzxAttendanceRecordVO> page(QgzxAttendanceRecordParam param) {
        // 如果需要按学生分组查询，则调用分组查询方法
        if (Boolean.TRUE.equals(param.getGroupByStudent())) {
            PageResult<QgzxStudentAttendanceStatVO> statResult = pageStudentAttendanceStat(param);
            // 返回空的详细记录结果，前端根据分组数据再查询详细记录
            return new PageResult<>(new ArrayList<>(), 0L);
        }

        MyMPJLambdaWrapper<QgzxAttendanceRecord, QgzxAttendanceRecordParam> wrapper = buildQueryWrapper(param);
        Page<QgzxAttendanceRecordVO> page = new Page<>(param.getPage(), param.getLimit());
        page = mapper.selectJoinPage(page, QgzxAttendanceRecordVO.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<QgzxAttendanceRecordVO> list(QgzxAttendanceRecordParam param) {
        // 如果需要按学生分组查询，则调用分组查询方法
        if (Boolean.TRUE.equals(param.getGroupByStudent())) {
            List<QgzxStudentAttendanceStatVO> statList = listStudentAttendanceStat(param);
            // 返回空的详细记录结果，前端根据分组数据再查询详细记录
            return new ArrayList<>();
        }

        MyMPJLambdaWrapper<QgzxAttendanceRecord, QgzxAttendanceRecordParam> wrapper = buildQueryWrapper(param);
        List<QgzxAttendanceRecordVO> list = mapper.selectJoinList(QgzxAttendanceRecordVO.class, wrapper);
        return list;
    }

    @Override
    public QgzxAttendanceRecordVO getDetail(String id) {
        QgzxAttendanceRecordVO detail = mapper.selectJoinOne(QgzxAttendanceRecordVO.class,
                buildQueryWrapper(new QgzxAttendanceRecordParam()).eq(QgzxAttendanceRecord::getId, id));
        return detail;
    }

    @Transactional
    @Override
    public void clockIn(QgzxAttendanceRecord record) {
        String currentUser = SecurityUtil.getUsername();
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = now.toLocalDate(); // 使用LocalDate确保只包含日期部分
        QgzxStudentApply studentApply = studentApplyService.getById(record.getStudentApplyId());
        AssertUtil.isTrue(studentApply != null, "学生申请不存在");
        AssertUtil.isTrue(Objects.equals(studentApply.getXgh(), currentUser), "无权操作此申请");
        AssertUtil.isTrue(WorkStatus.YG.equals(studentApply.getYgzt()), "学生申请状态不是用工状态，无法打卡");
        QgzxJobApplication jobApplication = jobApplicationService.getById(studentApply.getJobId());
        AssertUtil.isTrue(jobApplication != null, "岗位信息不存在");
        AssertUtil.isTrue(JudgeMark.YES.equals(jobApplication.getSfqd()), "该岗位未开启签到功能");
        // 使用工具类进行LocalDate精确匹配查询
        LambdaQueryWrapper<QgzxAttendanceRecord> clockInWrapper = new LambdaQueryWrapper<QgzxAttendanceRecord>()
                .eq(QgzxAttendanceRecord::getStudentApplyId, record.getStudentApplyId())
                .eq(QgzxAttendanceRecord::getAttendanceType, AttendanceType.CLOCK_IN);
        LocalDateQueryUtil.eqLocalDate(clockInWrapper, QgzxAttendanceRecord::getAttendanceDate, today);
        QgzxAttendanceRecord existingClockIn = getOne(clockInWrapper);
        AssertUtil.isTrue(existingClockIn == null, "今日已经上班打卡，无法重复打卡");
        record.setAttendanceDate(today);
        record.setAttendanceType(AttendanceType.CLOCK_IN);
        record.setClockTime(now);
        record.setCreateTime(now);
        Map<String, Object> validation = validateClockIn(record.getStudentApplyId(), record.getLongitude(), record.getLatitude());
        record.setIsNormal((JudgeMark) validation.get("isValid"));

        // 自动判断并设置打卡状态
        AttendanceStatus status = attendanceStatusService.determineAttendanceStatus(record, jobApplication);
        record.setAttendanceStatus(status);

        save(record);
    }

    @Transactional
    @Override
    public void clockOut(QgzxAttendanceRecord record) {
        String currentUser = SecurityUtil.getUsername();
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = now.toLocalDate();
        QgzxStudentApply studentApply = studentApplyService.getById(record.getStudentApplyId());
        AssertUtil.isTrue(studentApply != null, "学生申请不存在");
        AssertUtil.isTrue(Objects.equals(studentApply.getXgh(), currentUser), "无权操作此申请");
        AssertUtil.isTrue(WorkStatus.YG.equals(studentApply.getYgzt()), "学生申请状态不是用工状态，无法打卡");
        QgzxJobApplication jobApplication = jobApplicationService.getById(studentApply.getJobId());
        AssertUtil.isTrue(jobApplication != null, "岗位信息不存在");
        AssertUtil.isTrue(JudgeMark.YES.equals(jobApplication.getSfqd()), "该岗位未开启签到功能");

        LambdaQueryWrapper<QgzxAttendanceRecord> clockInWrapper = new LambdaQueryWrapper<QgzxAttendanceRecord>()
                .eq(QgzxAttendanceRecord::getStudentApplyId, record.getStudentApplyId())
                .eq(QgzxAttendanceRecord::getAttendanceType, AttendanceType.CLOCK_IN);
        LocalDateQueryUtil.eqLocalDate(clockInWrapper, QgzxAttendanceRecord::getAttendanceDate, today);
        QgzxAttendanceRecord clockInRecord = getOne(clockInWrapper);
        AssertUtil.isTrue(clockInRecord != null, "今日尚未上班打卡，无法下班打卡");
        
        LambdaQueryWrapper<QgzxAttendanceRecord> clockOutWrapper = new LambdaQueryWrapper<QgzxAttendanceRecord>()
                .eq(QgzxAttendanceRecord::getStudentApplyId, record.getStudentApplyId())
                .eq(QgzxAttendanceRecord::getAttendanceType, AttendanceType.CLOCK_OUT);
        LocalDateQueryUtil.eqLocalDate(clockOutWrapper, QgzxAttendanceRecord::getAttendanceDate, today);
        QgzxAttendanceRecord existingClockOut = getOne(clockOutWrapper);
        AssertUtil.isTrue(existingClockOut == null, "今日已经下班打卡，无法重复打卡");
        
        BigDecimal workHours = calculateWorkHoursBetween(clockInRecord.getClockTime(), now);
        record.setAttendanceDate(today);
        record.setAttendanceType(AttendanceType.CLOCK_OUT);
        record.setClockTime(now);
        record.setWorkDuration(workHours);
        record.setCreateTime(now);
        Map<String, Object> validation = validateClockIn(record.getStudentApplyId(), record.getLongitude(), record.getLatitude());
        record.setIsNormal((JudgeMark) validation.get("isValid"));

        // 自动判断并设置打卡状态
        AttendanceStatus status = attendanceStatusService.determineAttendanceStatus(record, jobApplication);
        record.setAttendanceStatus(status);

        save(record);
    }

    @Transactional
    @Override
    public void delete(String... ids) {
        String currentUser = SecurityUtil.getUsername();
        List<String> toDeleteIds = new ArrayList<>();

        for (String id : ids) {
            QgzxAttendanceRecord record = getById(id);
            AssertUtil.isTrue(record != null, "考勤记录不存在：" + id);
            checkDeletePermission(record, currentUser);
            checkBusinessConstraints(record);
            toDeleteIds.add(id);
            handlePairedRecord(record, toDeleteIds);
        }

        if (!toDeleteIds.isEmpty()) {
            removeBatchByIds(toDeleteIds);
        }
    }

    @Override
    public PageResult<QgzxAttendanceRecordVO> pageByEmployer(QgzxAttendanceRecordParam param) {
        String currentUser = SecurityUtil.getUsername();
        QgzxEmployer employer = employerService.getOne(
            new LambdaQueryWrapper<QgzxEmployer>().eq(QgzxEmployer::getXgh, currentUser)
        );
        AssertUtil.isTrue(employer != null, "未找到用人单位信息");
        param.setEmployerId(employer.getId());

        // 如果需要按学生分组查询，则调用分组查询方法
        if (Boolean.TRUE.equals(param.getGroupByStudent())) {
            PageResult<QgzxStudentAttendanceStatVO> statResult = pageStudentAttendanceStatByEmployer(param);
            // 返回空的详细记录结果，前端根据分组数据再查询详细记录
            return new PageResult<>(new ArrayList<>(), 0L);
        }

        return page(param);
    }

    @Override
    public List<QgzxAttendanceRecordVO> listByStudent(QgzxAttendanceRecordParam param) {
        String currentUser = SecurityUtil.getUsername();
        param.setXgh(currentUser);
        return list(param);
    }

    @Override
    public PageResult<QgzxAttendanceRecordVO> pageByStudent(QgzxAttendanceRecordParam param) {
        String currentUser = SecurityUtil.getUsername();
        param.setXgh(currentUser);
        return page(param);
    }

    @Override
    public Map<String, Object> getTodayAttendanceStatus(String studentApplyId) {
        LocalDate today = LocalDate.now();
        Map<String, Object> result = new HashMap<>();
        
        // 使用工具类查询今日上班打卡记录
        LambdaQueryWrapper<QgzxAttendanceRecord> clockInWrapper = new LambdaQueryWrapper<QgzxAttendanceRecord>()
                .eq(QgzxAttendanceRecord::getStudentApplyId, studentApplyId)
                .eq(QgzxAttendanceRecord::getAttendanceType, AttendanceType.CLOCK_IN);
        LocalDateQueryUtil.eqLocalDate(clockInWrapper, QgzxAttendanceRecord::getAttendanceDate, today);
        QgzxAttendanceRecord clockInRecord = getOne(clockInWrapper);
        
        // 使用工具类查询今日下班打卡记录
        LambdaQueryWrapper<QgzxAttendanceRecord> clockOutWrapper = new LambdaQueryWrapper<QgzxAttendanceRecord>()
                .eq(QgzxAttendanceRecord::getStudentApplyId, studentApplyId)
                .eq(QgzxAttendanceRecord::getAttendanceType, AttendanceType.CLOCK_OUT);
        LocalDateQueryUtil.eqLocalDate(clockOutWrapper, QgzxAttendanceRecord::getAttendanceDate, today);
        QgzxAttendanceRecord clockOutRecord = getOne(clockOutWrapper);
        
        result.put("hasClockedIn", clockInRecord != null);
        result.put("hasClockedOut", clockOutRecord != null);
        result.put("clockInTime", clockInRecord != null ? clockInRecord.getClockTime() : null);
        result.put("clockOutTime", clockOutRecord != null ? clockOutRecord.getClockTime() : null);
        
        if (clockInRecord != null) {
            LocalDateTime endTime = clockOutRecord != null ? clockOutRecord.getClockTime() : LocalDateTime.now();
            BigDecimal currentWorkHours = calculateWorkHoursBetween(clockInRecord.getClockTime(), endTime);
            long workMinutes = Duration.between(clockInRecord.getClockTime(), endTime).toMinutes();
            result.put("currentWorkMinutes", workMinutes);
            result.put("currentWorkHours", currentWorkHours);
        }
        return result;
    }

    @Override
    public BigDecimal calculateWorkHours(String studentApplyId, LocalDate startDate, LocalDate endDate) {
        List<QgzxAttendanceRecord> records = list(new LambdaQueryWrapper<QgzxAttendanceRecord>()
                .eq(QgzxAttendanceRecord::getStudentApplyId, studentApplyId)
                .eq(QgzxAttendanceRecord::getAttendanceType, AttendanceType.CLOCK_OUT)
                .ge(QgzxAttendanceRecord::getAttendanceDate, startDate)
                .le(QgzxAttendanceRecord::getAttendanceDate, endDate)
                .isNotNull(QgzxAttendanceRecord::getWorkDuration));
        BigDecimal totalHours = records.stream()
                .map(record -> record.getWorkDuration() != null ? record.getWorkDuration() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalHours;
    }

    @Override
    public Map<String, Object> validateClockIn(String studentApplyId, BigDecimal longitude, BigDecimal latitude) {
        Map<String, Object> result = new HashMap<>();
        JudgeMark isValid = JudgeMark.YES;
        StringBuilder message = new StringBuilder();
        QgzxStudentApply studentApply = studentApplyService.getById(studentApplyId);
        if (studentApply == null) {
            result.put("isValid", JudgeMark.NO);
            result.put("message", "学生申请不存在");
            return result;
        }
        QgzxJobApplication jobApplication = jobApplicationService.getById(studentApply.getJobId());
        if (jobApplication == null) {
            result.put("isValid", JudgeMark.NO);
            result.put("message", "岗位信息不存在");
            return result;
        }

        String timeValidationResult = validateWorkTime(jobApplication);
        if (timeValidationResult != null) {
            isValid = JudgeMark.NO;
            message.append(timeValidationResult);
        }
        //TODO 检查经纬度

        result.put("isValid", isValid);
        result.put("message", message.toString());
        return result;
    }

    private void checkDeletePermission(QgzxAttendanceRecord record, String currentUser) {
        if (SecurityUtil.hasAuthority("workstudy:qgzxAttendanceRecord:delete")) {
            return;
        }

        // 获取学生申请信息
        QgzxStudentApply studentApply = studentApplyService.getById(record.getStudentApplyId());
        AssertUtil.isTrue(studentApply != null, "关联的学生申请不存在");

        // 学生只能删除自己的考勤记录
        if (Objects.equals(studentApply.getXgh(), currentUser)) {
            return;
        }

        // 用人单位负责人可以删除本单位学生的考勤记录
        QgzxJobApplication jobApplication = jobApplicationService.getById(studentApply.getJobId());
        if (jobApplication != null) {
            QgzxEmployer employer = employerService.getOne(
                new LambdaQueryWrapper<QgzxEmployer>()
                    .eq(QgzxEmployer::getXgh, currentUser)
                    .eq(QgzxEmployer::getId, jobApplication.getEid())
            );
            if (employer != null) {
                return;
            }
        }
        AssertUtil.throwMessage("无权限删除此考勤记录");
    }

    private void checkBusinessConstraints(QgzxAttendanceRecord record) {
        // 检查学生申请状态
        QgzxStudentApply studentApply = studentApplyService.getById(record.getStudentApplyId());
        AssertUtil.isTrue(studentApply != null, "关联的学生申请不存在");
        AssertUtil.isTrue(WorkStatus.YG.equals(studentApply.getYgzt()), "学生申请状态异常，无法删除考勤记录");
    }

    /**
     * 处理配对的考勤记录
     */
    private void handlePairedRecord(QgzxAttendanceRecord record, List<String> toDeleteIds) {
        if (AttendanceType.CLOCK_IN.equals(record.getAttendanceType())) {
            // 如果删除上班打卡，需要同时删除对应的下班打卡
            // 使用工具类查询同日期的下班打卡记录
            LocalDate targetDate = record.getAttendanceDate();
            LambdaQueryWrapper<QgzxAttendanceRecord> clockOutWrapper = new LambdaQueryWrapper<QgzxAttendanceRecord>()
                .eq(QgzxAttendanceRecord::getStudentApplyId, record.getStudentApplyId())
                .eq(QgzxAttendanceRecord::getAttendanceType, AttendanceType.CLOCK_OUT);
            LocalDateQueryUtil.eqLocalDate(clockOutWrapper, QgzxAttendanceRecord::getAttendanceDate, targetDate);
            QgzxAttendanceRecord clockOutRecord = getOne(clockOutWrapper);

            if (clockOutRecord != null && !toDeleteIds.contains(clockOutRecord.getId())) {
                toDeleteIds.add(clockOutRecord.getId());
            }
        }
    }

    /**
     * 统一的工作时长计算方法（小时，保留2位小数）
     * 用于确保所有地方的计算逻辑一致
     */
    private BigDecimal calculateWorkHoursBetween(LocalDateTime startTime, LocalDateTime endTime) {
        Duration duration = Duration.between(startTime, endTime);
        long workMinutes = duration.toMinutes();
        return BigDecimal.valueOf(workMinutes).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
    }

    /**
     * 验证工作时间
     * @param jobApplication 岗位信息
     * @return 验证失败的错误信息，验证通过返回null
     */
    private String validateWorkTime(QgzxJobApplication jobApplication) {
        LocalDateTime now = LocalDateTime.now();
        if (StringUtils.hasText(jobApplication.getWorkDays())) {
            if (!isValidWorkDay(now, jobApplication.getWorkDays())) {
                return "当前不在工作日范围内；";
            }
        }
        if (StringUtils.hasText(jobApplication.getStartTime()) && StringUtils.hasText(jobApplication.getEndTime())) {
            if (!isValidWorkTime(now, jobApplication.getStartTime(), jobApplication.getEndTime())) {
                return "当前不在工作时间范围内；";
            }
        }
        return null;
    }

    /**
     * 验证是否在工作日范围内
     * @param dateTime 当前时间
     * @param workDays 工作日配置（如：1,2,3,4,5 表示周一到周五）
     * @return 是否在工作日范围内
     */
    private boolean isValidWorkDay(LocalDateTime dateTime, String workDays) {
        try {
            int currentDayOfWeek = dateTime.getDayOfWeek().getValue();
            String[] workDayArray = workDays.split(",");
            for (String workDay : workDayArray) {
                if (String.valueOf(currentDayOfWeek).equals(workDay.trim())) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 验证是否在工作时间范围内
     * @param dateTime 当前时间
     * @param startTime 开始时间（如：09:00）
     * @param endTime 结束时间（如：17:00）
     * @return 是否在工作时间范围内
     */
    private boolean isValidWorkTime(LocalDateTime dateTime, String startTime, String endTime) {
        try {
            // 获取当前时间（只考虑时分）
            int currentHour = dateTime.getHour();
            int currentMinute = dateTime.getMinute();
            int currentTimeInMinutes = currentHour * 60 + currentMinute;
            String[] startParts = startTime.split(":");
            int startHour = Integer.parseInt(startParts[0]);
            int startMinute = startParts.length > 1 ? Integer.parseInt(startParts[1]) : 0;
            int startTimeInMinutes = startHour * 60 + startMinute;
            String[] endParts = endTime.split(":");
            int endHour = Integer.parseInt(endParts[0]);
            int endMinute = endParts.length > 1 ? Integer.parseInt(endParts[1]) : 0;
            int endTimeInMinutes = endHour * 60 + endMinute;
            // 处理跨天的情况（如：22:00-06:00）
            if (endTimeInMinutes < startTimeInMinutes) {
                // 跨天情况：当前时间在开始时间之后或结束时间之前
                return currentTimeInMinutes >= startTimeInMinutes || currentTimeInMinutes <= endTimeInMinutes;
            } else {
                // 同一天情况：当前时间在开始和结束时间之间
                return currentTimeInMinutes >= startTimeInMinutes && currentTimeInMinutes <= endTimeInMinutes;
            }
        } catch (Exception e) {
            return true;
        }
    }

    private MyMPJLambdaWrapper<QgzxAttendanceRecord, QgzxAttendanceRecordParam> buildQueryWrapper(QgzxAttendanceRecordParam param) {
        MyMPJLambdaWrapper<QgzxAttendanceRecord, QgzxAttendanceRecordParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAll(QgzxAttendanceRecord.class);
        wrapper.selectAssociation("sa", QgzxStudentApply.class, QgzxAttendanceRecord::getStudentApply)
                .leftJoin(QgzxStudentApply.class, "sa", QgzxStudentApply::getId, QgzxAttendanceRecord::getStudentApplyId);
        wrapper.selectAssociation("u", UserInfo.class, QgzxAttendanceRecord::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh);
        wrapper.selectAssociation("j", QgzxJobApplication.class, QgzxAttendanceRecord::getJobApplication)
                .leftJoin(QgzxJobApplication.class, "j", QgzxJobApplication::getId, QgzxStudentApply::getJobId);
        wrapper.selectAs("e", QgzxEmployer::getName, QgzxAttendanceRecordVO::getEmployerName)
                .leftJoin(QgzxEmployer.class, "e", QgzxEmployer::getId, QgzxJobApplication::getEid);
        wrapper.selectAs("u", UserInfo::getXm, QgzxAttendanceRecordVO::getStudentName);
        wrapper.selectAs("u", UserInfo::getSjh, QgzxAttendanceRecordVO::getStudentContact);
        wrapper.selectAs("j", QgzxJobApplication::getJobName, QgzxAttendanceRecordVO::getJobName);
        if (StringUtils.hasText(param.getXm())) {
            wrapper.like("u.XM", param.getXm());
        }
        if (StringUtils.hasText(param.getJobName())) {
            wrapper.like("j.JOB_NAME", param.getJobName());
        }
        if (StringUtils.hasText(param.getEmployerId())) {
            wrapper.eq("j.EID", param.getEmployerId());
        }
        if (StringUtils.hasText(param.getXgh())) {
            wrapper.eq("sa.XGH", param.getXgh());
        }
        if (StringUtils.hasText(param.getJobId())) {
            wrapper.eq("sa.JOB_ID", param.getJobId());
        }
        if (StringUtils.hasText(param.getXnxq())) {
            wrapper.eq("sa.XNXQ", param.getXnxq());
        }
        if (param.getAttendanceDateStart() != null) {
            wrapper.ge(QgzxAttendanceRecord::getAttendanceDate, param.getAttendanceDateStart().atStartOfDay());
        }
        if (param.getAttendanceDateEnd() != null) {
            wrapper.le(QgzxAttendanceRecord::getAttendanceDate, param.getAttendanceDateEnd().atTime(23, 59, 59));
        }
        wrapper.orderByDesc(QgzxAttendanceRecord::getCreateTime);
        return wrapper;
    }

    @Override
    public List<QgzxAttendanceRecordVO> listByStatus(QgzxAttendanceRecordParam param) {
        MyMPJLambdaWrapper<QgzxAttendanceRecord, QgzxAttendanceRecordParam> wrapper = buildQueryWrapper(param);
        if (param.getAttendanceStatus() != null) {
            wrapper.eq(QgzxAttendanceRecord::getAttendanceStatus, param.getAttendanceStatus());
        }
        return mapper.selectJoinList(QgzxAttendanceRecordVO.class, wrapper);
    }

    @Override
    public Map<AttendanceStatus, Long> countByStatus(String studentApplyId, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<QgzxAttendanceRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QgzxAttendanceRecord::getStudentApplyId, studentApplyId)
               .ge(QgzxAttendanceRecord::getAttendanceDate, startDate)
               .le(QgzxAttendanceRecord::getAttendanceDate, endDate)
               .isNotNull(QgzxAttendanceRecord::getAttendanceStatus);

        List<QgzxAttendanceRecord> records = list(wrapper);

        return records.stream()
                .collect(Collectors.groupingBy(
                    QgzxAttendanceRecord::getAttendanceStatus,
                    Collectors.counting()
                ));
    }

    @Override
    public PageResult<QgzxAttendanceRecordVO> getAbnormalRecords(QgzxAttendanceRecordParam param) {
        MyMPJLambdaWrapper<QgzxAttendanceRecord, QgzxAttendanceRecordParam> wrapper = buildQueryWrapper(param);
        // 过滤异常状态：迟到、早退
        wrapper.in(QgzxAttendanceRecord::getAttendanceStatus,
                  AttendanceStatus.LATE,
                  AttendanceStatus.EARLY_LEAVE);

        Page<QgzxAttendanceRecordVO> page = new Page<>(param.getPage(), param.getLimit());
        page = mapper.selectJoinPage(page, QgzxAttendanceRecordVO.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public PageResult<QgzxStudentAttendanceStatVO> pageStudentAttendanceStat(QgzxAttendanceRecordParam param) {
        List<QgzxStudentAttendanceStatVO> allStats = listStudentAttendanceStat(param);

        // 手动分页
        int page = param.getPage().intValue();
        int limit = param.getLimit().intValue();
        int start = (page - 1) * limit;
        int end = Math.min(start + limit, allStats.size());

        List<QgzxStudentAttendanceStatVO> pageData = start < allStats.size() ?
            allStats.subList(start, end) : new ArrayList<>();

        return new PageResult<>(pageData, (long) allStats.size());
    }

    @Override
    public List<QgzxStudentAttendanceStatVO> listStudentAttendanceStat(QgzxAttendanceRecordParam param) {
        // 构建基础查询条件
        MyMPJLambdaWrapper<QgzxAttendanceRecord, QgzxAttendanceRecordParam> wrapper = buildQueryWrapper(param);

        // 查询所有符合条件的考勤记录
        List<QgzxAttendanceRecordVO> records = mapper.selectJoinList(QgzxAttendanceRecordVO.class, wrapper);

        // 按学生申请ID分组统计
        Map<String, List<QgzxAttendanceRecordVO>> groupedRecords = records.stream()
            .collect(Collectors.groupingBy(QgzxAttendanceRecordVO::getStudentApplyId));

        List<QgzxStudentAttendanceStatVO> statList = new ArrayList<>();

        for (Map.Entry<String, List<QgzxAttendanceRecordVO>> entry : groupedRecords.entrySet()) {
            String studentApplyId = entry.getKey();
            List<QgzxAttendanceRecordVO> studentRecords = entry.getValue();

            QgzxStudentAttendanceStatVO stat = buildStudentAttendanceStat(studentApplyId, studentRecords);
            statList.add(stat);
        }

        // 按最近考勤时间倒序排列
        statList.sort((a, b) -> {
            if (a.getLastAttendanceTime() == null && b.getLastAttendanceTime() == null) return 0;
            if (a.getLastAttendanceTime() == null) return 1;
            if (b.getLastAttendanceTime() == null) return -1;
            return b.getLastAttendanceTime().compareTo(a.getLastAttendanceTime());
        });

        return statList;
    }

    @Override
    public PageResult<QgzxStudentAttendanceStatVO> pageStudentAttendanceStatByEmployer(QgzxAttendanceRecordParam param) {
        String currentUser = SecurityUtil.getUsername();
        QgzxEmployer employer = employerService.getOne(
            new LambdaQueryWrapper<QgzxEmployer>().eq(QgzxEmployer::getXgh, currentUser)
        );
        AssertUtil.isTrue(employer != null, "未找到用人单位信息");
        param.setEmployerId(employer.getId());
        return pageStudentAttendanceStat(param);
    }

    /**
     * 构建学生考勤统计数据
     */
    private QgzxStudentAttendanceStatVO buildStudentAttendanceStat(String studentApplyId, List<QgzxAttendanceRecordVO> records) {
        QgzxStudentAttendanceStatVO stat = new QgzxStudentAttendanceStatVO();

        if (records.isEmpty()) {
            return stat;
        }

        // 获取第一条记录的基本信息
        QgzxAttendanceRecordVO firstRecord = records.get(0);
        stat.setStudentApplyId(studentApplyId);
        stat.setXgh(firstRecord.getStudentApply() != null ? firstRecord.getStudentApply().getXgh() : null);
        stat.setStudentName(firstRecord.getStudentName());
        stat.setStudentContact(firstRecord.getStudentContact());
        stat.setJobId(firstRecord.getJobApplication() != null ? firstRecord.getJobApplication().getId() : null);
        stat.setJobName(firstRecord.getJobName());
        stat.setEmployerName(firstRecord.getEmployerName());
        stat.setXnxq(firstRecord.getStudentApply() != null ? firstRecord.getStudentApply().getXnxq() : null);

        // 统计各种状态的数量
        Map<AttendanceStatus, Long> statusCount = records.stream()
            .filter(r -> r.getAttendanceStatus() != null)
            .collect(Collectors.groupingBy(
                QgzxAttendanceRecordVO::getAttendanceStatus,
                Collectors.counting()
            ));

        stat.setStatusStatistics(statusCount);
        stat.setTotalRecords((long) records.size());
        stat.setNormalCount(statusCount.getOrDefault(AttendanceStatus.NORMAL, 0L));
        stat.setLateCount(statusCount.getOrDefault(AttendanceStatus.LATE, 0L));
        stat.setEarlyLeaveCount(statusCount.getOrDefault(AttendanceStatus.EARLY_LEAVE, 0L));
        stat.setAbsentCount(0L); // 暂时不考虑缺勤状态，设置为0
        stat.setLeaveCount(statusCount.getOrDefault(AttendanceStatus.LEAVE, 0L));

        // 计算总工作时长（只统计下班打卡记录的工作时长）
        BigDecimal totalWorkHours = records.stream()
            .filter(r -> AttendanceType.CLOCK_OUT.equals(r.getAttendanceType()) && r.getWorkDuration() != null)
            .map(QgzxAttendanceRecordVO::getWorkDuration)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stat.setTotalWorkHours(totalWorkHours);

        // 获取最近考勤信息
        Optional<QgzxAttendanceRecordVO> latestRecord = records.stream()
            .max(Comparator.comparing(QgzxAttendanceRecordVO::getCreateTime));
        if (latestRecord.isPresent()) {
            stat.setLastAttendanceDate(latestRecord.get().getAttendanceDate());
            stat.setLastAttendanceTime(latestRecord.get().getClockTime());
        }

        // 计算正常率
        if (stat.getTotalRecords() > 0) {
            BigDecimal normalRate = BigDecimal.valueOf(stat.getNormalCount())
                .divide(BigDecimal.valueOf(stat.getTotalRecords()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            stat.setNormalRate(normalRate);
        } else {
            stat.setNormalRate(BigDecimal.ZERO);
        }

        return stat;
    }

    @Transactional
    @Override
    public void updateAttendanceStatus(String recordId, AttendanceStatus status) {
        LambdaUpdateWrapper<QgzxAttendanceRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(QgzxAttendanceRecord::getId, recordId)
                    .set(QgzxAttendanceRecord::getAttendanceStatus, status);
        boolean updated = this.update(updateWrapper);
        if (updated) {
            log.info("更新考勤记录状态成功，记录ID: {}, 新状态: {}", recordId, status.getText());
        } else {
            log.warn("更新考勤记录状态失败，记录ID: {}", recordId);
        }
    }
}
