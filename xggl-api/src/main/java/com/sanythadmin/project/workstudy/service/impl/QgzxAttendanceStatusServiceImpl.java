package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workstudy.entity.QgzxAttendanceRecord;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import com.sanythadmin.project.workstudy.enums.AttendanceType;
import com.sanythadmin.project.workstudy.mapper.QgzxAttendanceRecordMapper;
import com.sanythadmin.project.workstudy.service.QgzxAttendanceStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 考勤状态判断服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
public class QgzxAttendanceStatusServiceImpl extends ServiceImpl<QgzxAttendanceRecordMapper,QgzxAttendanceRecord> implements QgzxAttendanceStatusService {

    @Override
    public AttendanceStatus determineAttendanceStatus(QgzxAttendanceRecord record, QgzxJobApplication jobApplication) {
        if (isOnLeave(record.getStudentApplyId(), record.getClockTime())) {
            return AttendanceStatus.LEAVE;
        }
        if (!StringUtils.hasText(jobApplication.getStartTime()) ||
            !StringUtils.hasText(jobApplication.getEndTime())) {
            return AttendanceStatus.NORMAL;
        }
        return determineStatusByWorkSchedule(record, jobApplication);
    }

    @Override
    public AttendanceStatus determineStatusByTime(LocalDateTime clockTime,LocalDateTime scheduledTime,AttendanceType attendanceType) {
        if (attendanceType == AttendanceType.CLOCK_IN) {
            // 上班打卡：超过规定时间
            if (clockTime.isAfter(scheduledTime)) {
                return AttendanceStatus.LATE;
            }
        } else if (attendanceType == AttendanceType.CLOCK_OUT) {
            // 下班打卡：早于规定时间
            if (clockTime.isBefore(scheduledTime)) {
                return AttendanceStatus.EARLY_LEAVE;
            }
        }

        return AttendanceStatus.NORMAL;
    }

    @Override
    public boolean isOnLeave(String studentApplyId, LocalDateTime attendanceDate) {
        // TODO: 实现请假状态检查逻辑
        return false;
    }

    // 注意：数据库更新操作已移至 QgzxAttendanceRecordService 中，避免循环依赖
    // 此方法现在只负责状态判断逻辑，不直接操作数据库

    private AttendanceStatus determineStatusByWorkSchedule(QgzxAttendanceRecord record, QgzxJobApplication jobApplication) {
        try {
            LocalDateTime clockTime = record.getClockTime();
            LocalDate attendanceDate = record.getAttendanceDate();
            AttendanceType attendanceType = record.getAttendanceType();
            LocalTime startTime = LocalTime.parse(jobApplication.getStartTime());
            LocalTime endTime = LocalTime.parse(jobApplication.getEndTime());
            LocalDateTime scheduledDateTime;
            if (attendanceType == AttendanceType.CLOCK_IN) {
                // 上班打卡
                scheduledDateTime = attendanceDate.atTime(startTime);
            } else {
                // 下班打卡
                scheduledDateTime = attendanceDate.atTime(endTime);
            }
            return determineStatusByTime(clockTime, scheduledDateTime, attendanceType);
        } catch (Exception e) {
            return AttendanceStatus.NORMAL;
        }
    }

}
