package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workstudy.entity.QgzxAttendanceRecord;
import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import com.sanythadmin.project.workstudy.param.QgzxAttendanceRecordParam;
import com.sanythadmin.project.workstudy.service.QgzxAttendanceRecordService;
import com.sanythadmin.project.workstudy.service.QgzxAttendanceStatusService;
import com.sanythadmin.project.workstudy.vo.QgzxAttendanceRecordVO;
import com.sanythadmin.project.workstudy.vo.QgzxStudentAttendanceStatVO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 勤工助学/考勤记录控制器
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-attendance-record")
@RequiredArgsConstructor
public class QgzxAttendanceRecordController extends BaseController {

    private final QgzxAttendanceRecordService attendanceRecordService;
    private final QgzxAttendanceStatusService attendanceStatusService;

    /**
     * 分页查询考勤记录（权限标识：workstudy:qgzxAttendanceRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:list')")
    @GetMapping("/page")
    public PageResult<QgzxAttendanceRecordVO> page(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.page(param);
    }

    /**
     * 查询考勤记录列表（权限标识：workstudy:qgzxAttendanceRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:list')")
    @GetMapping("/list")
    public List<QgzxAttendanceRecordVO> list(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.list(param);
    }

    /**
     * 根据ID查询考勤记录详情（权限标识：workstudy:qgzxAttendanceRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:list')")
    @GetMapping("/detail/{id}")
    public QgzxAttendanceRecordVO getDetail(@PathVariable String id) {
        return attendanceRecordService.getDetail(id);
    }

    /**
     * 学生上班打卡（权限标识：workstudy:qgzxAttendanceRecord:clockIn）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:clockIn')")
    @OperationLog(module = "考勤记录", comments = "学生上班打卡")
    @PostMapping("/clock-in")
    public void clockIn(@RequestBody QgzxAttendanceRecord record) {
        attendanceRecordService.clockIn(record);
    }

    /**
     * 学生下班打卡（权限标识：workstudy:qgzxAttendanceRecord:clockOut）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:clockOut')")
    @OperationLog(module = "考勤记录", comments = "学生下班打卡")
    @PostMapping("/clock-out")
    public void clockOut(@RequestBody QgzxAttendanceRecord record) {
        attendanceRecordService.clockOut(record);
    }

    /**
     * 删除考勤记录（权限标识：workstudy:qgzxAttendanceRecord:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:remove')")
    @OperationLog(module = "考勤记录", comments = "删除考勤记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        attendanceRecordService.delete(ids.toArray(new String[]{}));
    }

    /**
     * 用人单位分页查询考勤记录（权限标识：workstudy:qgzxAttendanceRecord:listByEmployer）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:listByEmployer')")
    @GetMapping("/page-by-employer")
    public PageResult<QgzxAttendanceRecordVO> pageByEmployer(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.pageByEmployer(param);
    }

    /**
     * 学生查询自己的考勤记录（权限标识：workstudy:qgzxAttendanceRecord:listByStudent）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:listByStudent')")
    @GetMapping("/list-by-student")
    public List<QgzxAttendanceRecordVO> listByStudent(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.listByStudent(param);
    }

    /**
     * 学生分页查询自己的考勤记录（权限标识：workstudy:qgzxAttendanceRecord:listByStudent）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:listByStudent')")
    @GetMapping("/page-by-student")
    public PageResult<QgzxAttendanceRecordVO> pageByStudent(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.pageByStudent(param);
    }

    /**
     * 获取学生今日考勤状态（权限标识：workstudy:qgzxAttendanceRecord:todayStatus）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:todayStatus')")
    @GetMapping("/today-status/{studentApplyId}")
    public Map<String, Object> getTodayAttendanceStatus(@PathVariable String studentApplyId) {
        return attendanceRecordService.getTodayAttendanceStatus(studentApplyId);
    }

    /**
     * 统计学生工作时长（权限标识：workstudy:qgzxAttendanceRecord:calculateWorkHours）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:calculateWorkHours')")
    @GetMapping("/calculate-work-hours")
    public BigDecimal calculateWorkHours(@RequestParam String studentApplyId,@RequestParam LocalDate startDate,@RequestParam LocalDate endDate) {
        return attendanceRecordService.calculateWorkHours(studentApplyId, startDate, endDate);
    }

    /**
     * 根据状态查询考勤记录
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:list')")
    @GetMapping("/list-by-status")
    public List<QgzxAttendanceRecordVO> listByStatus(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.listByStatus(param);
    }

    /**
     * 统计各种考勤状态的数量
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:list')")
    @GetMapping("/count-by-status")
    public Map<AttendanceStatus, Long> countByStatus(@RequestParam String studentApplyId,@RequestParam LocalDate startDate,@RequestParam LocalDate endDate) {
        return attendanceRecordService.countByStatus(studentApplyId, startDate, endDate);
    }

    /**
     * 获取异常考勤记录（迟到、早退）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:list')")
    @GetMapping("/abnormal-records")
    public PageResult<QgzxAttendanceRecordVO> getAbnormalRecords(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.getAbnormalRecords(param);
    }

    /**
     * 更新考勤记录状态
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:update')")
    @OperationLog(module = "考勤记录",comments = "更新考勤记录状态")
    @PostMapping("/update-status/{recordId}")
    public void updateAttendanceStatus(@PathVariable String recordId,@RequestParam AttendanceStatus status) {
        attendanceRecordService.updateAttendanceStatus(recordId, status);
    }

    /**
     * 按学生分组查询考勤统计数据（权限标识：workstudy:qgzxAttendanceRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:list')")
    @GetMapping("/student-stat-page")
    public PageResult<QgzxStudentAttendanceStatVO> pageStudentAttendanceStat(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.pageStudentAttendanceStat(param);
    }

    /**
     * 按学生分组查询考勤统计数据列表（权限标识：workstudy:qgzxAttendanceRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:list')")
    @GetMapping("/student-stat-list")
    public List<QgzxStudentAttendanceStatVO> listStudentAttendanceStat(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.listStudentAttendanceStat(param);
    }

    /**
     * 用人单位按学生分组查询考勤统计数据（权限标识：workstudy:qgzxAttendanceRecord:listByEmployer）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxAttendanceRecord:listByEmployer')")
    @GetMapping("/student-stat-page-by-employer")
    public PageResult<QgzxStudentAttendanceStatVO> pageStudentAttendanceStatByEmployer(QgzxAttendanceRecordParam param) {
        return attendanceRecordService.pageStudentAttendanceStatByEmployer(param);
    }

}
