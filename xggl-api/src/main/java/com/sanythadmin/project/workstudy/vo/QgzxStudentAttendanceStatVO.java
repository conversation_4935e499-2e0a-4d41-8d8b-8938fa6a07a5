package com.sanythadmin.project.workstudy.vo;

import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 学生考勤统计VO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class QgzxStudentAttendanceStatVO {

    /**
     * 学生申请ID
     */
    private String studentApplyId;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 学生联系方式
     */
    private String studentContact;

    /**
     * 岗位ID
     */
    private String jobId;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 用人单位名称
     */
    private String employerName;

    /**
     * 学年学期
     */
    private String xnxq;

    /**
     * 总考勤记录数
     */
    private Long totalRecords;

    /**
     * 正常考勤次数
     */
    private Long normalCount;

    /**
     * 迟到次数
     */
    private Long lateCount;

    /**
     * 早退次数
     */
    private Long earlyLeaveCount;

    /**
     * 缺勤次数
     */
    private Long absentCount;

    /**
     * 请假次数
     */
    private Long leaveCount;

    /**
     * 总工作时长（小时）
     */
    private BigDecimal totalWorkHours;

    /**
     * 最近考勤日期
     */
    private LocalDate lastAttendanceDate;

    /**
     * 最近考勤时间
     */
    private LocalDateTime lastAttendanceTime;

    /**
     * 各状态统计详情
     */
    private Map<AttendanceStatus, Long> statusStatistics;

    /**
     * 考勤正常率（百分比）
     */
    private BigDecimal normalRate;

}
