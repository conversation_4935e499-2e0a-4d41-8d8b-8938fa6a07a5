package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.workstudy.entity.QgzxAttendanceRecord;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import com.sanythadmin.project.workstudy.enums.AttendanceType;

import java.time.LocalDateTime;

/**
 * 考勤状态判断服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface QgzxAttendanceStatusService extends IService<QgzxAttendanceRecord> {

    /**
     * 根据业务规则自动判断打卡状态
     *
     * @param record 考勤记录
     * @param jobApplication 岗位信息
     * @return 打卡状态
     */
    AttendanceStatus determineAttendanceStatus(QgzxAttendanceRecord record, QgzxJobApplication jobApplication);

    /**
     * 根据打卡时间和规定时间判断状态
     *
     * @param clockTime 实际打卡时间
     * @param scheduledTime 规定时间
     * @param attendanceType 考勤类型（上班/下班）
     * @return 打卡状态
     */
    AttendanceStatus determineStatusByTime(LocalDateTime clockTime,LocalDateTime scheduledTime,AttendanceType attendanceType);

    /**
     * 检查是否为请假状态
     *
     * @param studentApplyId 学生申请ID
     * @param attendanceDate 考勤日期
     * @return 是否请假
     */
    boolean isOnLeave(String studentApplyId, LocalDateTime attendanceDate);

    // 注意：updateAttendanceStatus 方法已移至 QgzxAttendanceRecordService 中，避免循环依赖
}
