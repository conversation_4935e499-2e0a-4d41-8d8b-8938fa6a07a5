#!/bin/bash

# 简单的SSH连接测试
SERVER_HOST="*************"
SERVER_USER="root"
SERVER_PASSWORD="Sanyth123!"
SERVER_PORT="22"

echo "测试SSH连接到 $SERVER_USER@$SERVER_HOST:$SERVER_PORT"
echo "=============================================="

# 检查sshpass是否安装
if ! command -v sshpass >/dev/null 2>&1; then
    echo "错误: sshpass未安装"
    echo "请运行: ./install-sshpass.sh"
    exit 1
fi

echo "1. 测试基本SSH连接（正确的命令）..."
if sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
    -o ConnectTimeout=15 \
    -o StrictHostKeyChecking=no \
    -o UserKnownHostsFile=/dev/null \
    -o PasswordAuthentication=yes \
    "$SERVER_USER@$SERVER_HOST" "echo 'SSH连接成功'" 2>/dev/null; then
    echo "✓ SSH连接测试成功！"
else
    echo "✗ SSH连接失败"
    echo ""
    echo "详细错误信息："
    sshpass -p "$SERVER_PASSWORD" ssh -v -p "$SERVER_PORT" \
        -o ConnectTimeout=15 \
        -o StrictHostKeyChecking=no \
        -o UserKnownHostsFile=/dev/null \
        -o PasswordAuthentication=yes \
        "$SERVER_USER@$SERVER_HOST" "echo 'SSH连接成功'" 2>&1 | head -20
    exit 1
fi

echo ""
echo "2. 测试远程命令执行..."
sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
    -o ConnectTimeout=15 \
    -o StrictHostKeyChecking=no \
    -o UserKnownHostsFile=/dev/null \
    -o PasswordAuthentication=yes \
    "$SERVER_USER@$SERVER_HOST" "whoami && hostname && pwd"

echo ""
echo "3. 测试目标目录..."
REMOTE_APP_DIR="/home/<USER>/xuegong-v5"
if sshpass -p "$SERVER_PASSWORD" ssh -p "$SERVER_PORT" \
    -o ConnectTimeout=15 \
    -o StrictHostKeyChecking=no \
    -o UserKnownHostsFile=/dev/null \
    -o PasswordAuthentication=yes \
    "$SERVER_USER@$SERVER_HOST" "[ -d '$REMOTE_APP_DIR' ] && echo '目录存在' || echo '目录不存在'"; then
    echo "目录检查完成"
else
    echo "目录检查失败"
fi

echo ""
echo "=============================================="
echo "SSH连接测试完成！"
echo "如果以上测试都成功，现在可以运行: ./deploy.sh"
